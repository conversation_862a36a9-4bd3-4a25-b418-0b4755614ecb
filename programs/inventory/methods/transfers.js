import _ from 'lodash';
import microtime from 'microtime';
import {escapeRegExp, firstUpperAll, generateRandomBarcode, toLower, toUpper} from 'framework/helpers';
import Random from 'framework/random';
import EWaybill from '../../eops/utils/e-waybill';
import ecommerceIntegrations from '../../ecommerce/integrations';

export default [
    {
        name: 'transfers-save-transfer',
        async action({data, id}, params) {
            const app = this.app;
            const isCreate = !id;
            const company = await app.collection('kernel.company').findOne({});
            const hasSubItems = (data.items ?? []).some(
                item => Array.isArray(item.subItems) && item.subItems.length > 0
            );

            // Exchange rates map.
            const exchangeRatesMap = {};
            for (const exchangeRate of data.exchangeRates || []) {
                exchangeRatesMap[exchangeRate.currencyName] = exchangeRate.rate;
            }
            data.exchangeRatesMap = exchangeRatesMap;

            if (!isCreate) {
                // Check permission.
                await app.checkPermission({
                    user: params.user,
                    collection: 'inventory.transfers',
                    method: 'patch',
                    id
                });

                // Check the transfer is already approved.
                if (data.status === 'approved' && !!params.user) {
                    const existing = await app.collection('inventory.transfers').findOne({
                        _id: id,
                        $select: ['_id', 'status']
                    });

                    if (!!existing && existing.status === 'approved') {
                        throw new Error('Transfer is ready approved!!!');
                    }
                }

                if (data.status === 'approved' && !app.setting('inventory.useBulkEntryInTransfers') && !hasSubItems) {
                    throw new Error(app.translate('Transfer items are invalid!'));
                }

                try {
                    let subItems = [];
                    if (!!app.setting('inventory.useBulkEntryInTransfers') && !hasSubItems) {
                        subItems = (
                            await app.collection('inventory.transfer-sub-items').find({
                                transferId: id
                            })
                        ).map(subItem => {
                            subItem.quantity = subItem.qty;

                            return subItem;
                        });

                        const productIds = _.uniq((data.items ?? []).map(item => item.productId));
                        const products = await app.collection('inventory.products').find({
                            _id: {$in: productIds},
                            $select: ['_id', 'baseUnitId', 'unitRatios'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        const productsMap = {};
                        for (const product of products) {
                            productsMap[product._id] = product;
                        }

                        const report = await app.collection('inventory.transfer-sub-items').aggregate([
                            {
                                $match: {
                                    transferId: id
                                }
                            },
                            {
                                $group: {
                                    _id: '$itemId',
                                    qty: {$sum: '$qty'}
                                }
                            }
                        ]);
                        data.items = (data.items ?? []).map(item => {
                            const product = productsMap[item.productId];
                            const unitRatio = ((product || {}).unitRatios || {})[item.unitId] || 1;
                            const r = report.find(r => r._id === item.id);

                            item.actualQty = !!r ? app.roundNumber(r.qty / unitRatio, 4) : 0;

                            return item;
                        });
                    } else {
                        for (const item of data.items) {
                            for (const subItem of item.subItems || []) {
                                subItems.push({
                                    ...subItem,
                                    itemId: item.id
                                });
                            }
                        }
                    }

                    // Run assignation, and validation workflow.
                    if (app.hasModule('workflow') && data.status !== 'approved') {
                        const flowResult = await app.rpc(
                            'workflow.run-workflow',
                            {
                                name: 'inventory.transfers',
                                data: data,
                                id,
                                operation: 'update',
                                actionTypes: ['validation']
                            },
                            {user: params.user}
                        );
                        data = flowResult.data;
                    }

                    const operationType = await app.collection('inventory.operation-types').findOne({
                        _id: data.operationTypeId,
                        $select: ['type', 'warehouseId']
                    });
                    const warehouse = await app.collection('inventory.warehouses').findOne({
                        _id: operationType.warehouseId
                    });

                    if (data.status === 'approved' && data.items.some(item => !(item.requestedQty > 0))) {
                        throw new app.errors.Unprocessable(
                            app.translate('Requested quantity must be greater than zero!')
                        );
                    }

                    if (data.status === 'approved') {
                        // Check serial numbers.
                        const serialNumbers = [];
                        for (const subItem of subItems) {
                            if (!!subItem.serialNumber && serialNumbers.includes(subItem.serialNumber)) {
                                throw new app.errors.Unprocessable(
                                    app.translate('All the serial numbers must be unique!')
                                );
                            }

                            serialNumbers.push(subItem.serialNumber);
                        }
                    }

                    if (data.status === 'approved') {
                        const periodsCollection = app.collection('kernel.periods');

                        // Get and check fiscal year.
                        const fiscalYear = await periodsCollection.findOne({
                            'periods.recordDateStart': {$lte: data.recordDate},
                            'periods.recordDateEnd': {$gte: data.recordDate}
                        });
                        if (!_.isObject(fiscalYear)) {
                            throw new app.errors.Unprocessable(
                                this.translate('No relevant fiscal year found according to the supplied record date!')
                            );
                        }

                        // Get and check accounting period.
                        const period = fiscalYear.periods.find(period => {
                            return (
                                period.recordDateStart.getTime() <= data.recordDate.getTime() &&
                                period.recordDateEnd.getTime() >= data.recordDate.getTime()
                            );
                        });
                        if (!_.isObject(period)) {
                            throw new app.errors.Unprocessable(
                                this.translate(
                                    'No relevant accounting period found according to the supplied record date!'
                                )
                            );
                        }

                        // Check accounting period status.
                        if (period.status === 'blockedExceptForAdvisers') {
                            if (!!params.user) {
                                if (!params.user.isAdviser) {
                                    throw new app.errors.Unprocessable(
                                        this.translate('The relevant accounting period has been blocked!')
                                    );
                                }
                            } else {
                                throw new app.errors.Unprocessable(
                                    this.translate('The relevant accounting period has been blocked!')
                                );
                            }
                        } else if (period.status !== 'unBlocked') {
                            throw new app.errors.Unprocessable(
                                this.translate('The relevant accounting period has been blocked!')
                            );
                        }

                        // Check issue date.
                        if (
                            !(
                                period.issueDateStart.getTime() <= data.issueDate.getTime() &&
                                period.issueDateEnd.getTime() >= data.issueDate.getTime()
                            )
                        ) {
                            throw new app.errors.Unprocessable(
                                this.translate(
                                    'The issue date you entered is invalid for the {{periodName}} period. Please update the date and try again.',
                                    {periodName: period.name}
                                )
                            );
                        }
                    }

                    if (
                        data.status === 'approved' &&
                        operationType.type === 'incoming' &&
                        !app.setting('inventory.allowMovesWithoutCost') &&
                        data.items.some(item => item.unitPrice === 0)
                    ) {
                        const item = data.items.find(item => item.unitPrice === 0);

                        throw new app.errors.Unprocessable(
                            this.translate(
                                'The transaction cannot be performed because the item with the product code {{code}} has no cost!',
                                {code: item.productCode}
                            )
                        );
                    }

                    if (data.status === 'approved' && operationType.type === 'incoming') {
                        const products = await app.collection('inventory.products').find({
                            _id: {$in: data.items.map(item => item.productId)},
                            $select: ['code', 'cost', 'valuationMethod', 'tracking'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        const productsMap = {};
                        for (const product of products) {
                            productsMap[product._id] = product;
                        }

                        for (const item of data.items) {
                            const product = productsMap[item.productId];

                            // Check product costs.
                            if (!!product && product.valuationMethod === 'standard' && product.cost === 0) {
                                throw new app.errors.Unprocessable(
                                    app.translate('No standard cost found for the product with code {{code}}!', {
                                        code: product.code
                                    })
                                );
                            }
                        }

                        // Check serial numbers.
                        const serialNumbers = [];
                        for (const subItem of subItems) {
                            if (!!subItem.serialNumber) {
                                serialNumbers.push(subItem.serialNumber);
                            }
                        }
                        if (serialNumbers.length > 0) {
                            for (const serialNumber of serialNumbers) {
                                const count = await app.collection('inventory.quantities').count({
                                    'location.type': 'internal',
                                    warehouseId: warehouse._id,
                                    serialNumber
                                });

                                if (count > 0) {
                                    throw new app.errors.Unprocessable(
                                        app.translate('Serial number "{{serialNumber}}" exists in the system!', {
                                            serialNumber
                                        })
                                    );
                                }
                            }
                        }
                    }

                    if (data.status === 'approved' && operationType.type === 'outgoing') {
                        const products = await app.collection('inventory.products').find({
                            _id: {$in: data.items.map(item => item.productId)},
                            $select: ['_id', 'code', 'definition', 'negativeStock', 'unitRatios', 'tracking'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        const productsMap = {};
                        for (const product of products) {
                            productsMap[product._id] = product;
                        }

                        const serialNumbers = [];
                        for (const item of data.items) {
                            const product = productsMap[item.productId];
                            const stockQuantity = item.stockQuantity / ((product.unitRatios ?? {})[item.unitId] ?? 1);

                            if (stockQuantity - item.actualQty < 0 && !product.negativeStock) {
                                const message = 'Not enough stock available for the product {{product}}!';

                                throw new app.errors.Unprocessable(
                                    this.translate(message, {
                                        product: `${product.code} - ${product.definition}`
                                    })
                                );
                            }
                        }
                        for (const subItem of subItems) {
                            if (!!subItem.serialNumber) {
                                serialNumbers.push(subItem.serialNumber);
                            }
                        }
                        if (serialNumbers.length > 0) {
                            for (const serialNumber of serialNumbers) {
                                const count = await app.collection('inventory.quantities').count({
                                    'location.type': 'internal',
                                    warehouseId: warehouse._id,
                                    serialNumber
                                });

                                if (count < 1) {
                                    throw new app.errors.Unprocessable(
                                        app.translate(
                                            'Serial number "{{serialNumber}}" does not exists in the system!',
                                            {
                                                serialNumber
                                            }
                                        )
                                    );
                                }
                            }
                        }

                        // Check stocks.
                        if (!!app.setting('inventory.useBulkEntryInTransfers') && !hasSubItems) {
                            // Prepare the items.
                            const lotItems = [];
                            const barcodeItems = [];
                            for (const subItem of subItems) {
                                if (!!subItem.lotNumber) {
                                    lotItems.push(subItem);
                                } else if (!subItem.lotNumber && !subItem.serialNumber) {
                                    barcodeItems.push(subItem);
                                }
                            }

                            // Check stocks for lot items.
                            for (const lotItem of lotItems) {
                                const sum = await app.collection('inventory.quantities').sum('qty', {
                                    'location.type': 'internal',
                                    warehouseId: warehouse._id,
                                    lotNumber: lotItem.lotNumber,
                                    locationId: lotItem.sourceLocationId
                                });

                                if (sum < lotItem.qty) {
                                    throw new app.errors.Unprocessable(
                                        this.translate(
                                            'Not enough stock available for the product {{product}} and for the lot number {{lotNumber}} at the location {{location}}!',
                                            {
                                                product: `${lotItem.productCode} - ${lotItem.productDefinition}`,
                                                lotNumber: lotItem.lotNumber,
                                                location: lotItem.sourceLocationPath
                                            }
                                        )
                                    );
                                }
                            }

                            // Check stocks for barcode items.
                            for (const barcodeItem of barcodeItems) {
                                const product = productsMap[barcodeItem.productId];
                                const negativeStock = !!(product ?? {}).negativeStock;
                                const sum = await app.collection('inventory.quantities').sum('qty', {
                                    productId: barcodeItem.productId,
                                    'location.type': 'internal',
                                    warehouseId: warehouse._id,
                                    locationId: barcodeItem.sourceLocationId
                                });

                                if (sum < barcodeItem.qty && !negativeStock) {
                                    throw new app.errors.Unprocessable(
                                        this.translate(
                                            'Not enough stock available for the product {{product}} at the location {{location}}!',
                                            {
                                                product: `${barcodeItem.productCode} - ${barcodeItem.productDefinition}`,
                                                location: barcodeItem.sourceLocationPath
                                            }
                                        )
                                    );
                                }
                            }
                        }
                    }

                    // Check product warehouse blockage.
                    if (data.status === 'approved') {
                        const pwb = await app.rpc(
                            'inventory.get-product-warehouse-blockage',
                            data.items.map(item => ({
                                warehouseId: warehouse._id,
                                productId: item.productId
                            }))
                        );

                        if (!!pwb) {
                            throw new app.errors.Unprocessable(pwb.message);
                        }
                    }

                    const oldTransfer = await app.collection('inventory.transfers').findOne({
                        _id: id,
                        $select: [
                            'status',
                            'type',
                            'connectedTransferId',
                            'relatedDocuments',
                            'reference',
                            'referenceId',
                            'referenceCollection',
                            'documentType'
                        ]
                    });

                    // Create partial delivery.
                    let partialDeliveryPayload = null;
                    if (data.status === 'approved') {
                        if (!!data.createPartialDelivery) {
                            const now = app.datetime.local().toJSDate();
                            const scheduledDate = app.datetime
                                .fromJSDate(data.scheduledDate)
                                .plus({minutes: 1})
                                .toJSDate();
                            const items = [];

                            for (let i = 0; i < data.items.length; i++) {
                                const item = _.cloneDeep(data.items[i]);

                                if (item.requestedQty > item.actualQty) {
                                    items.push({
                                        ..._.omit(item, [
                                            'actualQty',
                                            'requestedQty',
                                            'subItems',
                                            'availableQuantity',
                                            'assignedQuantity',
                                            'orderedQuantity',
                                            'stockQuantity',
                                            'id'
                                        ]),
                                        quantity: item.requestedQty - item.actualQty,
                                        productType: 'stockable',
                                        warehouseId: warehouse._id,
                                        scheduledDate,
                                        deliveryReceiverId: data.deliveryReceiverId,
                                        deliveryAddressId: data.deliveryAddressId,
                                        deliveryAddress: data.deliveryAddress
                                    });

                                    data.items[i].requestedQty = item.actualQty;
                                    data.items[i].partialQty = item.requestedQty - item.actualQty;
                                }
                            }
                            if (items.length > 0) {
                                partialDeliveryPayload = {
                                    warehouseOperationType: operationType.type,
                                    documentType: oldTransfer.documentType,
                                    sourceLocationId: data.sourceLocationId,
                                    destinationLocationId: data.destinationLocationId,
                                    journalDescription: data.journalDescription,
                                    partnerId: data.partnerId,
                                    warehouseId: warehouse._id,
                                    recordDate: now,
                                    issueDate: now,
                                    dueDate: data.dueDate,
                                    scheduledDate,
                                    reference: oldTransfer.reference,
                                    referenceId: oldTransfer.referenceId,
                                    referenceCollection: oldTransfer.referenceCollection,
                                    deliveryPriority: data.deliveryPriority,
                                    deliveryPolicy: data.deliveryPolicy,
                                    deliveryConditionId: data.deliveryConditionId,
                                    deliveryMethodId: data.deliveryMethodId,
                                    shippingDocumentType: data.shippingDocumentType,
                                    invoiceResponsibleId: data.invoiceResponsibleId,
                                    invoiceAddressId: data.invoiceAddressId,
                                    invoiceAddress: data.invoiceAddress,
                                    financialProjectId: data.financialProjectId,
                                    currencyId: data.currencyId,
                                    currencyRate: data.currencyRate || 1,
                                    exchangeRates: data.exchangeRates || [],
                                    exchangeRatesMap: data.exchangeRatesMap || {},
                                    items,
                                    relatedDocuments: [
                                        {
                                            collection: 'inventory.transfers',
                                            view: 'inventory.operations.transfers',
                                            title: 'Deliveries',
                                            ids: [id]
                                        }
                                    ]
                                };
                            }
                        }

                        if (data.items.some(item => item.actualQty !== item.requestedQty)) {
                            throw new app.errors.Unprocessable(
                                app.translate('All the requested amounts must be completed!')
                            );
                        }
                    }

                    // Ready status.
                    if (
                        (data.status === 'draft' || data.status === 'waiting') &&
                        Array.isArray(data.items) &&
                        data.items.length > 0 &&
                        !data.items.some(item => item.actualQty !== item.requestedQty)
                    ) {
                        data.status = 'ready';
                    }

                    if (!!oldTransfer.connectedTransferId) {
                        const connectedTransfer = await app.collection('inventory.transfers').findOne({
                            _id: oldTransfer.connectedTransferId,
                            $select: ['status']
                        });

                        if (connectedTransfer.status !== 'approved') {
                            if (data.status !== 'approved') {
                                data.status = 'waiting';
                            } else {
                                throw new app.errors.Unprocessable(
                                    app.translate(
                                        'For this transfer to be approved, connected transfer must be approved!'
                                    )
                                );
                            }
                        }
                    }

                    if (data.status === 'approved' && operationType.type === 'outgoing') {
                        const connectedTransfer = await app.collection('inventory.transfers').findOne({
                            connectedTransferId: id,
                            $select: ['_id', 'status', 'sourceLocationId', 'destinationLocationId', 'items'],
                            $disableSoftDelete: true,
                            $disableActiveCheck: true
                        });
                        const sourceLocation = await app.collection('inventory.locations').findOne({
                            _id: connectedTransfer?.sourceLocationId,
                            $disableSoftDelete: true,
                            $disableActiveCheck: true
                        });
                        const destinationLocation = await app.collection('inventory.locations').findOne({
                            _id: connectedTransfer?.destinationLocation,
                            $disableSoftDelete: true,
                            $disableActiveCheck: true
                        });
                        const now = app.datetime.local().toJSDate();

                        if (!!connectedTransfer) {
                            if (app.setting('inventory.useBulkEntryInTransfers') && !hasSubItems) {
                                const data = {};

                                if (app.setting('inventory.copyTransportLotAndSerialNumbers')) {
                                    const allOutgoingSubItems = await app
                                        .collection('inventory.transfer-sub-items')
                                        .find({
                                            transferId: id
                                        });
                                    const transferSubItemsOperations = [];

                                    for (const item of data.items ?? []) {
                                        const connectedItem = (connectedTransfer.items ?? []).find(
                                            ci => ci.productId === item.productId && ci.unitId === item.unitId
                                        );
                                        if (!connectedItem) {
                                            continue;
                                        }

                                        const outgoingSubItems = allOutgoingSubItems.filter(
                                            si => si.itemId === item.id
                                        );
                                        if (outgoingSubItems.length < 1) {
                                            continue;
                                        }

                                        for (const outgoingSubItem of outgoingSubItems) {
                                            const d = {...outgoingSubItem};

                                            d.transferId = connectedTransfer._id;
                                            d.itemId = connectedItem.id;
                                            d.id = Random.id(16);
                                            d.status = 'draft';
                                            d.sourceLocationId = sourceLocation?._id;
                                            d.sourceLocationPath = sourceLocation?.path;
                                            d.destinationLocationId = destinationLocation?._id;
                                            d.destinationLocationPath = destinationLocation?.path;
                                            d.createdAt = now;
                                            d.updatedAt = now;

                                            transferSubItemsOperations.push({
                                                insertOne: {
                                                    document: d
                                                }
                                            });
                                        }

                                        connectedItem.actualQty = item.actualQty;
                                        connectedItem.cost =
                                            (connectedItem.unitPriceAfterDiscount ?? 0) * connectedItem.actualQty +
                                            (connectedItem.freight ?? 0);
                                        connectedItem.total =
                                            (connectedItem.unitPriceAfterDiscount ?? 0) * connectedItem.actualQty;
                                        connectedItem.subItems = [];
                                    }

                                    if (transferSubItemsOperations.length > 0) {
                                        await app
                                            .collection('inventory.transfer-sub-items')
                                            .bulkWrite(transferSubItemsOperations);

                                        data.items = connectedTransfer.items ?? [];
                                    }
                                }

                                data.status = connectedTransfer.status;
                                if (
                                    (connectedTransfer.status === 'draft' || connectedTransfer.status === 'waiting') &&
                                    (connectedTransfer.items ?? []).length > 0 &&
                                    !(connectedTransfer.items ?? []).some(item => item.actualQty !== item.requestedQty)
                                ) {
                                    data.status = 'ready';
                                } else {
                                    data.status = 'waiting';
                                }

                                await app.collection('inventory.transfers').patch({_id: connectedTransfer._id}, data, {
                                    user: params.user,
                                    checkPermission: false
                                });
                            } else {
                                for (const item of data.items ?? []) {
                                    const connectedItem = (connectedTransfer.items ?? []).find(
                                        ci => ci.productId === item.productId && ci.unitId === item.unitId
                                    );
                                    const itemSubItems = subItems.filter(subItem => subItem.itemId === item.id);
                                    if (!connectedItem) {
                                        continue;
                                    }

                                    const sns = [];
                                    for (const si of itemSubItems) {
                                        if (!!si.serialNumber && !sns.includes(si.serialNumber)) {
                                            sns.push(si.serialNumber);
                                        }
                                    }
                                    const serialNumbersMap = {};
                                    if (sns.length > 0) {
                                        const serialNumbers = await app.collection('inventory.serial-numbers').find({
                                            serialNumber: {$in: sns}
                                        });

                                        for (const serialNumber of serialNumbers) {
                                            serialNumbersMap[serialNumber.serialNumber] = serialNumber;
                                        }
                                    }

                                    const lns = [];
                                    for (const si of itemSubItems) {
                                        if (!!si.lotNumber && !lns.includes(si.lotNumber)) {
                                            lns.push(si.lotNumber);
                                        }
                                    }
                                    const lotNumbersMap = {};
                                    if (lns.length > 0) {
                                        const lotNumbers = await app.collection('inventory.lot-numbers').find({
                                            lotNumber: {$in: lns}
                                        });

                                        for (const lotNumber of lotNumbers) {
                                            lotNumbersMap[lotNumber.lotNumber] = lotNumber;
                                        }
                                    }

                                    const csis = [];
                                    for (const si of itemSubItems) {
                                        const csi = {};

                                        csi.serialNumber = si.serialNumber;
                                        csi.lotNumber = si.lotNumber;
                                        csi.expirationDate = si.expirationDate;
                                        csi.acceptanceDate = now;
                                        csi.quantity = si.quantity;
                                        csi.sourceLocationId = connectedTransfer.sourceLocationId;
                                        csi.destinationLocationId = connectedTransfer.destinationLocationId;

                                        if (!!si.lotNumber) {
                                            const lotNumber = lotNumbersMap[si.lotNumber];

                                            if (!!lotNumber) {
                                                csi.productionDate = lotNumber.productionDate;
                                                csi.expirationDate = lotNumber.expirationDate;
                                                csi.manufacturerSerialNumber = lotNumber.manufacturerSerialNumber;
                                                csi.manufacturerWarrantyStartDate =
                                                    lotNumber.manufacturerWarrantyStartDate;
                                                csi.manufacturerWarrantyEndDate = lotNumber.manufacturerWarrantyEndDate;
                                            }
                                        }
                                        if (!!si.serialNumber) {
                                            const serialNumber = serialNumbersMap[si.serialNumber];

                                            if (!!serialNumber) {
                                                csi.productionDate = serialNumber.productionDate;
                                                csi.expirationDate = serialNumber.expirationDate;
                                                csi.manufacturerSerialNumber = serialNumber.manufacturerSerialNumber;
                                                csi.manufacturerWarrantyStartDate =
                                                    serialNumber.manufacturerWarrantyStartDate;
                                                csi.manufacturerWarrantyEndDate =
                                                    serialNumber.manufacturerWarrantyEndDate;
                                            }
                                        }

                                        csis.push(csi);
                                    }

                                    connectedItem.actualQty = item.actualQty;
                                    connectedItem.cost =
                                        (connectedItem.unitPriceAfterDiscount ?? 0) * connectedItem.actualQty +
                                        (connectedItem.freight ?? 0);
                                    connectedItem.total =
                                        (connectedItem.unitPriceAfterDiscount ?? 0) * connectedItem.actualQty;
                                    connectedItem.subItems = csis;
                                }

                                let status = connectedTransfer.status;
                                if (
                                    (connectedTransfer.status === 'draft' || connectedTransfer.status === 'waiting') &&
                                    (connectedTransfer.items ?? []).length > 0 &&
                                    !(connectedTransfer.items ?? []).some(item => item.actualQty !== item.requestedQty)
                                ) {
                                    status = 'ready';
                                } else {
                                    status = 'waiting';
                                }

                                await app.collection('inventory.transfers').patch(
                                    {_id: connectedTransfer._id},
                                    {
                                        status,
                                        items: connectedTransfer.items
                                    },
                                    {user: params.user, checkPermission: false}
                                );
                            }
                        }
                    }

                    try {
                        // Copyable items
                        if (data.status === 'approved' && !!data.partnerId) {
                            const products = await app.collection('inventory.products').find({
                                _id: {$in: _.uniq((data.items ?? []).map(item => item.productId))},
                                $select: ['_id', 'baseUnitId', 'salesTaxId', 'purchaseTaxId', 'unitRatios'],
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });
                            const productsMap = {};
                            for (const product of products) {
                                productsMap[product._id] = product;
                            }

                            const unitIds = [];
                            for (const item of data.items ?? []) {
                                const product = productsMap[item.productId];

                                if (!unitIds.includes(item.unitId)) {
                                    unitIds.push(item.unitId);
                                }
                                if (!unitIds.includes(product.baseUnitId)) {
                                    unitIds.push(product.baseUnitId);
                                }
                            }
                            const units = await app.collection('kernel.units').find({
                                _id: {$in: unitIds},
                                $select: ['_id', 'name'],
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });
                            const unitsMap = {};
                            for (const unit of units) {
                                unitsMap[unit._id] = unit;
                            }
                            const partner = await app.collection('kernel.partners').findOne({
                                _id: data.partnerId,
                                $select: ['_id', 'code', 'name'],
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });
                            const copyableItems = [];

                            for (const item of data.items ?? []) {
                                if (!(item.actualQty > 0)) {
                                    continue;
                                }

                                const product = productsMap[item.productId];
                                const unit = unitsMap[item.unitId];
                                const baseUnit = unitsMap[product.baseUnitId];
                                const unitRatio = (product.unitRatios ?? {})[item.unitId] ?? 1;
                                const copyableItem = {};

                                let type = 'delivery-order';
                                let documentView = 'inventory.operations.delivery-orders-detail';
                                let documentTitle = 'Delivery Orders';
                                if (
                                    operationType.type === 'incoming' &&
                                    operationType.documentType !== 'return-transfers'
                                ) {
                                    type = 'goods-receipt';
                                    documentView = 'inventory.operations.goods-receipts-detail';
                                    documentTitle = 'Goods Receipts';
                                }
                                if (
                                    operationType.type === 'incoming' &&
                                    operationType.documentType === 'return-transfers'
                                ) {
                                    type = 'return-goods-receipt';
                                    documentView = 'inventory.operations.return-goods-receipts-detail';
                                    documentTitle = 'Return Goods Receipts';
                                }
                                if (
                                    operationType.type === 'outgoing' &&
                                    operationType.documentType === 'return-transfers'
                                ) {
                                    type = 'return-delivery-order';
                                    documentView = 'inventory.operations.return-delivery-orders-detail';
                                    documentTitle = 'Return Delivery Orders';
                                }

                                copyableItem.type = type;
                                copyableItem.documentId = id;
                                copyableItem.documentCode = data.code;
                                copyableItem.documentNo = data.documentNo;
                                copyableItem.documentCollection = 'inventory.transfers';
                                copyableItem.documentView = documentView;
                                copyableItem.documentTitle = documentTitle;
                                copyableItem.partnerId = data.partnerId;
                                copyableItem.partnerCode = partner.code;
                                copyableItem.partnerName = partner.name;
                                copyableItem.productId = item.productId;
                                copyableItem.productCode = item.productCode;
                                copyableItem.productDefinition = item.productDefinition;
                                copyableItem.barcode = item.barcode ?? '';
                                copyableItem.unitId = item.unitId;
                                copyableItem.unitName = (unit ?? {}).name;
                                copyableItem.baseUnitId = product.baseUnitId;
                                copyableItem.baseUnitName = (baseUnit ?? {}).name;
                                copyableItem.quantity = item.actualQty;
                                copyableItem.remainingQuantity = item.actualQty;
                                copyableItem.unitRatio = unitRatio;
                                copyableItem.branchId = data.branchId;
                                copyableItem.currencyId = company.currencyId;
                                copyableItem.currencyId = data.currencyId;
                                copyableItem.currencyRate = data.currencyRate || 1;
                                copyableItem.unitPrice = item.unitPrice;
                                copyableItem.discount = item.discount;
                                copyableItem.taxIds = [];

                                if (
                                    (operationType.type === 'incoming' && data.documentType === 'return-transfers') ||
                                    (operationType.type === 'outgoing' && data.documentType !== 'return-transfers')
                                ) {
                                    copyableItem.taxIds = [product.salesTaxId];
                                } else if (
                                    (operationType.type === 'incoming' && data.documentType !== 'return-transfers') ||
                                    (operationType.type === 'outgoing' && data.documentType === 'return-transfers')
                                ) {
                                    copyableItem.taxIds = [product.purchaseTaxId];
                                }
                                copyableItem.issueDate = data.issueDate;
                                copyableItem.dueDate = data.dueDate;

                                copyableItems.push(copyableItem);
                            }

                            if (copyableItems.length > 0) {
                                await app.collection('kernel.copyable-items').create(copyableItems);
                            }
                        }
                    } catch (error) {}

                    // Sync copyable items.
                    if (data.status === 'approved' && data.items.some(item => !!item.copyableItemId)) {
                        const copyableItems = await app.collection('kernel.copyable-items').find({
                            _id: {
                                $in: data.items.filter(item => !!item.copyableItemId).map(item => item.copyableItemId)
                            }
                        });
                        const relatedDocuments = data.relatedDocuments ?? [];
                        const operations = [];

                        const products = await app.collection('inventory.products').find({
                            _id: {$in: _.uniq((data.items ?? []).map(item => item.productId))},
                            $select: ['_id', 'baseUnitId', 'salesTaxId', 'purchaseTaxId', 'unitRatios'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        const productsMap = {};
                        for (const product of products) {
                            productsMap[product._id] = product;
                        }

                        for (const item of data.items) {
                            const product = productsMap[item.productId];
                            const unitRatio = (product.unitRatios ?? {})[item.unitId] ?? 1;

                            if (!item.copyableItemId) {
                                continue;
                            }

                            const copyableItem = copyableItems.find(ci => ci._id === item.copyableItemId);
                            if (!copyableItem) {
                                continue;
                            }

                            const usage = copyableItem.usages.find(usage => usage.documentId === id);
                            if (!usage) {
                                continue;
                            }

                            let baseRemainingQuantity = copyableItem.remainingQuantity * (copyableItem.unitRatio ?? 1);
                            baseRemainingQuantity -= item.actualQty * unitRatio;
                            const remainingQuantity = Math.max(
                                baseRemainingQuantity / (copyableItem.unitRatio ?? 1),
                                0
                            );

                            operations.push({
                                updateOne: {
                                    filter: {_id: copyableItem._id},
                                    update: {
                                        $set: {
                                            remainingQuantity
                                        }
                                    }
                                }
                            });

                            const rdIndex = relatedDocuments.findIndex(
                                rd => rd.collection === copyableItem.documentCollection
                            );
                            if (rdIndex !== -1) {
                                relatedDocuments[rdIndex].ids.push(copyableItem.documentId);
                                relatedDocuments[rdIndex].ids = _.uniq(relatedDocuments[rdIndex].ids);
                            } else {
                                relatedDocuments.push({
                                    collection: copyableItem.documentCollection,
                                    view: copyableItem.documentView.replace('-detail', ''),
                                    title: copyableItem.documentTitle,
                                    ids: [copyableItem.documentId]
                                });
                            }

                            const er = await app.collection('inventory.external-reservations').findOne({
                                documentId: copyableItem.documentId,
                                documentCollection: copyableItem.documentCollection,
                                productId: copyableItem.productId,
                                warehouseId: warehouse._id,
                                $or: [{ordered: {$gt: 0}}, {assigned: {$gt: 0}}],
                                $select: ['_id', 'type', 'ordered', 'assigned']
                            });
                            if (!!er) {
                                if (er.type === 'outgoing') {
                                    await app.collection('inventory.external-reservations').patch(
                                        {_id: er._id},
                                        {
                                            assigned: Math.max((er.assigned ?? 0) - item.actualQty * unitRatio, 0)
                                        },
                                        {
                                            skipEvents: true
                                        }
                                    );
                                } else if (er.type === 'incoming') {
                                    await app.collection('inventory.external-reservations').patch(
                                        {_id: er._id},
                                        {
                                            ordered: Math.max((er.ordered ?? 0) - item.actualQty * unitRatio, 0)
                                        },
                                        {
                                            skipEvents: true
                                        }
                                    );
                                }
                            }
                        }

                        if (operations.length > 0) {
                            await app.collection('kernel.copyable-items').bulkWrite(operations);
                        }

                        for (const copyableItem of copyableItems) {
                            const document = await app.collection(copyableItem.documentCollection).findOne({
                                _id: copyableItem.documentId,
                                $select: ['_id', 'transferIds', 'relatedDocuments']
                            });

                            if (!!document) {
                                const transferIds = _.uniq((document.transferIds ?? []).concat(id));
                                const documentRelatedDocuments = document.relatedDocuments ?? [];

                                const rdIndex = documentRelatedDocuments.findIndex(
                                    rd => rd.collection === 'inventory.transfers'
                                );
                                if (rdIndex !== -1) {
                                    documentRelatedDocuments[rdIndex].ids.push(id);
                                    documentRelatedDocuments[rdIndex].ids = _.uniq(
                                        documentRelatedDocuments[rdIndex].ids
                                    );
                                } else {
                                    documentRelatedDocuments.push({
                                        collection: 'inventory.transfers',
                                        view: 'inventory.operations.transfers',
                                        title: 'Deliveries',
                                        ids: [id]
                                    });
                                }

                                await app.collection(copyableItem.documentCollection).patch(
                                    {_id: copyableItem.documentId},
                                    {
                                        transferIds,
                                        relatedDocuments: documentRelatedDocuments
                                    },
                                    {
                                        skipEvents: true
                                    }
                                );
                            }
                        }

                        data.relatedDocuments = relatedDocuments;
                    }

                    if (data.status === 'approved') {
                        let result = null;
                        let isReturnWaybill =
                            operationType.type === 'incoming' && data.documentType === 'return-transfers';

                        if (isReturnWaybill && !!data.partnerId) {
                            const partner = await app.collection('kernel.partners').findOne({
                                _id: data.partnerId,
                                $select: ['isCompany', 'identity', 'tin'],
                                $disableSoftDelete: true,
                                $disableActiveCheck: true
                            });

                            if (_.isPlainObject(partner)) {
                                const partnerTinIdentity = partner.isCompany ? partner.tin : partner.identity;

                                const eWaybill = new EWaybill({
                                    app,
                                    username: app.setting('eops.eWaybillUsername'),
                                    password: app.setting('eops.eWaybillPassword'),
                                    suLabel: app.setting('eops.eWaybillSULabel'),
                                    pbLabel: app.setting('eops.eWaybillPBLabel'),
                                    tin: company.tin
                                });
                                const users = await eWaybill.getUsers(partnerTinIdentity);

                                isReturnWaybill = users.length < 1;
                            }
                        } else {
                            isReturnWaybill = false;
                        }

                        // Create outgoing logistics document.
                        if (
                            (oldTransfer.type === 'outgoing' || isReturnWaybill) &&
                            (data.shippingDocumentType === 'waybill' || data.shippingDocumentType === 'invoice')
                        ) {
                            const numbering = await app.collection('kernel.numbering').findOne({
                                code: 'outboundLogisticsNumbering',
                                $select: ['_id'],
                                $disableInUseCheck: true,
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });
                            const now = app.datetime.local().toJSDate();
                            const logistics = {};

                            let scheduledDate = data.scheduledDate;
                            if (_.isDate(scheduledDate) && scheduledDate.getTime() < now.getTime()) {
                                scheduledDate = now;
                            }

                            logistics.isReturnLogistic = data.documentType === 'return-transfers';
                            logistics.transferId = id;
                            logistics.status = 'draft';
                            logistics.code = await app.rpc('kernel.common.request-number', {
                                numberingId: numbering._id,
                                save: true
                            });
                            logistics.type = 'outbound';
                            logistics.shippingDocumentType = data.shippingDocumentType;
                            logistics.trackingCode = data.cargoTrackingCode;
                            logistics.shippingPaymentType = data.shippingPaymentType;
                            logistics.partnerId = data.partnerId;
                            logistics.contactPersonId = data.deliveryReceiverId;
                            logistics.reference = data.code;
                            logistics.recordDate = app.datetime.local().toJSDate();
                            logistics.issueDate = data.scheduledDate;
                            logistics.deliveryDate = scheduledDate;
                            logistics.branchId = data.branchId;
                            logistics.currencyId = company.currencyId;
                            logistics.note = data.shipmentNote;
                            logistics.deliveryPriority = data.deliveryPriority;
                            logistics.deliveryConditionId = data.deliveryConditionId;
                            logistics.deliveryMethodId = data.deliveryMethodId;
                            logistics.carrierId = data.carrierId;
                            logistics.warehouseId = warehouse._id;
                            logistics.deliveryAddress = data.deliveryAddress;
                            logistics.deliveryAddressCode = data.deliveryAddressCode;
                            logistics.financialProjectId = data.financialProjectId;
                            logistics.partnerOrderReference = data.partnerOrderReference;
                            logistics.partnerOrderDate = data.partnerOrderDate;
                            logistics.supplierAddress = company.address;
                            logistics.warehouseAddress = warehouse.address;
                            logistics.relatedDocuments = [
                                {
                                    collection: 'inventory.transfers',
                                    view: 'inventory.operations.transfers',
                                    title: 'Deliveries',
                                    ids: [id]
                                }
                            ];
                            logistics.items = [];
                            logistics.total = 0;
                            logistics.isReturnWaybill = isReturnWaybill;

                            if (!!logistics.financialProjectId) {
                                const financialProject = await app.collection('kernel.financial-projects').findOne({
                                    _id: logistics.financialProjectId,
                                    $select: ['code', 'name'],
                                    $disableActiveCheck: true,
                                    $disableSoftDelete: true
                                });

                                if (!!financialProject) {
                                    logistics.financialProjectCode = financialProject.code;
                                    logistics.financialProjectName = financialProject.name;
                                }
                            }

                            if (!!data.partnerId) {
                                const partner = await app.collection('kernel.partners').findOne({
                                    _id: data.partnerId,
                                    $select: [
                                        'code',
                                        'name',
                                        'isCompany',
                                        'email',
                                        'phone',
                                        'identity',
                                        'tin',
                                        'taxDepartment'
                                    ],
                                    $disableSoftDelete: true,
                                    $disableActiveCheck: true
                                });

                                if (_.isPlainObject(partner)) {
                                    logistics.partnerIsCompany = !!partner.isCompany;
                                    logistics.partnerCode = partner.code;
                                    logistics.partnerName = !!partner.isCompany
                                        ? partner.legalName || partner.name
                                        : partner.name;
                                    logistics.partnerTinIdentity = !!partner.isCompany ? partner.tin : partner.identity;
                                    logistics.partnerTaxDepartment = partner.taxDepartment;
                                    logistics.partnerEmail = partner.email;
                                    logistics.partnerPhone = partner.phone;
                                }
                            }

                            if (data.documentType === 'return-transfers' && operationType.type === 'incoming') {
                                logistics.note = !!logistics.note
                                    ? `${logistics.note} - ${app.translate('This is a return waybill.')}`
                                    : app.translate('This is a return waybill.');
                                logistics.partnerIsCompany = true;
                                logistics.partnerName = company.legalName || company.name;
                                logistics.partnerTinIdentity = company.tin;
                                logistics.partnerTaxDepartment = company.taxDepartment;
                                logistics.partnerEmail = company.email;
                                logistics.partnerPhone = company.phone;
                                logistics.deliveryAddress = warehouse.address;

                                if (!!app.setting('system.multiBranch')) {
                                    const branch = await app.collection('kernel.branches').findOne({
                                        _id: logistics.branchId,
                                        $select: ['name', 'address'],
                                        $disableActiveCheck: true,
                                        $disableSoftDelete: true
                                    });

                                    if (!!branch) {
                                        logistics.partnerName = `${company.legalName || company.name} - ${branch.name}`;
                                    }
                                } else {
                                    logistics.partnerName = company.legalName || company.name;
                                }
                            }

                            if (oldTransfer.referenceCollection === 'inventory.transports') {
                                const transport = await app.collection('inventory.transports').findOne({
                                    _id: oldTransfer.referenceId,
                                    $select: ['destinationWarehouseId', 'destinationBranchId'],
                                    $disableSoftDelete: true
                                });
                                const destinationWarehouse = await app.collection('inventory.warehouses').findOne({
                                    _id: transport.destinationWarehouseId,
                                    $select: ['address'],
                                    $disableSoftDelete: true,
                                    $disableActiveCheck: true
                                });

                                if (!!app.setting('system.multiBranch')) {
                                    const branch = await app.collection('kernel.branches').findOne({
                                        _id: transport.destinationBranchId,
                                        $select: ['name', 'address'],
                                        $disableActiveCheck: true,
                                        $disableSoftDelete: true
                                    });

                                    if (!!branch) {
                                        logistics.partnerName = `${company.legalName || company.name} - ${branch.name}`;
                                    }
                                } else {
                                    logistics.partnerName = company.legalName || company.name;
                                }

                                logistics.partnerIsCompany = true;
                                logistics.partnerTinIdentity = company.tin;
                                logistics.partnerTaxDepartment = company.taxDepartment;
                                logistics.partnerEmail = company.email;
                                logistics.partnerPhone = company.phone;
                                logistics.deliveryAddress = destinationWarehouse.address;
                            }

                            for (const transferItem of data.items) {
                                const item = {};

                                if (!(transferItem.actualQty > 0)) {
                                    continue;
                                }

                                item.id = transferItem.id;
                                item.productId = transferItem.productId;
                                item.productCode = transferItem.productCode;
                                item.productDefinition = transferItem.productDefinition;
                                item.description = transferItem.description;
                                item.barcode = transferItem.barcode;
                                item.barcode = transferItem.barcode;
                                item.financialProjectId = transferItem.financialProjectId;
                                item.unitId = transferItem.unitId;
                                item.requestedQty = transferItem.requestedQty + (transferItem.partialQty || 0);
                                item.deliveredQty = transferItem.actualQty;

                                if (data.documentType === 'return-transfers') {
                                    item.unitPrice = 0;
                                    item.total = 0;
                                } else {
                                    item.unitPrice = transferItem.unitPrice;
                                    item.total = app.round(transferItem.unitPrice * transferItem.actualQty, 'total');
                                }

                                logistics.items.push(item);

                                logistics.total += app.round(item.unitPrice * item.deliveredQty, 'total');
                            }

                            // Check tin identity.
                            if (
                                !logistics.partnerTinIdentity ||
                                logistics.partnerTinIdentity.length < 1 ||
                                typeof logistics.partnerTinIdentity !== 'string'
                            ) {
                                throw new app.errors.Unprocessable(
                                    this.translate('Provided tin or identity is invalid!')
                                );
                            }

                            const outboundLogistics = await app
                                .collection('logistics.logistics')
                                .create(logistics, {user: params.user});

                            data.logisticId = outboundLogistics._id;

                            data.relatedDocuments = (
                                (Array.isArray(data.relatedDocuments) && data.relatedDocuments.length > 0
                                    ? data.relatedDocuments
                                    : oldTransfer.relatedDocuments) || []
                            ).concat([
                                {
                                    collection: 'logistics.logistics',
                                    view: 'logistics.operations.outbound-logistics',
                                    title: 'Outbound Logistics',
                                    ids: [outboundLogistics._id]
                                }
                            ]);
                        }

                        try {
                            const operations = [];

                            result = await app
                                .collection('inventory.transfers')
                                .patch({_id: id}, data, {user: params.user});

                            const connectedTransfers = await app.collection('inventory.transfers').find({
                                connectedTransferId: result._id,
                                $select: ['status', 'items']
                            });
                            for (const connectedTransfer of connectedTransfers || []) {
                                if (
                                    connectedTransfer.status === 'waiting' &&
                                    Array.isArray(connectedTransfer.items) &&
                                    connectedTransfer.items.length > 0 &&
                                    !connectedTransfer.items.some(item => item.actualQty !== item.requestedQty)
                                ) {
                                    operations.push({
                                        updateOne: {
                                            filter: {_id: connectedTransfer._id},
                                            update: {$set: {status: 'ready'}}
                                        }
                                    });
                                }
                            }

                            if (!!partialDeliveryPayload) {
                                let transfers = await app.rpc(
                                    'inventory.create-transfer',
                                    {
                                        from: 'partial-transfer',
                                        data: partialDeliveryPayload
                                    },
                                    {user: params.user}
                                );

                                // Fix: Filter undefined transfers.
                                if (Array.isArray(transfers)) {
                                    transfers = transfers.filter(transfer => !_.isUndefined(transfer));
                                }

                                if (!_.isUndefined(transfers)) {
                                    const transferIds = (Array.isArray(transfers) ? transfers : [transfers]).map(
                                        transfer => transfer._id
                                    );

                                    if (transferIds.length > 0) {
                                        operations.push({
                                            updateOne: {
                                                filter: {_id: id},
                                                update: {
                                                    $set: {
                                                        relatedDocuments: _.cloneDeep(
                                                            data.relatedDocuments || []
                                                        ).concat([
                                                            {
                                                                collection: 'inventory.transfers',
                                                                view: 'inventory.operations.transfers',
                                                                title: 'Deliveries',
                                                                ids: transferIds
                                                            }
                                                        ])
                                                    }
                                                }
                                            }
                                        });

                                        data.relatedDocuments = _.cloneDeep(data.relatedDocuments || []).concat([
                                            {
                                                collection: 'inventory.transfers',
                                                view: 'inventory.operations.transfers',
                                                title: 'Deliveries',
                                                ids: transferIds
                                            }
                                        ]);
                                    }
                                }
                            }

                            if (operations.length > 0) {
                                await app.collection('inventory.transfers').bulkWrite(operations);
                            }
                        } catch (error) {
                            await app.collection('inventory.transfers').patch(
                                {_id: id},
                                {
                                    status: oldTransfer.status
                                },
                                {user: params.user}
                            );

                            throw error;
                        }

                        if (
                            result &&
                            result.integrationPayload &&
                            result.integrationPayload.isIntegrationTransfer &&
                            operationType.type === 'incoming'
                        ) {
                            // noinspection ES6MissingAwait
                            (async () => {
                                try {
                                    const payload = {};
                                    payload.documentId = result._id;
                                    payload.documentCode = result.code;
                                    payload.items = (result.items || []).map(item => ({
                                        id: item.productId,
                                        code: item.productCode,
                                        name: item.productDefinition,
                                        quantity: item.actualQty
                                    }));
                                    payload.warehouseId = (result.operationType || {}).warehouseId;
                                    payload.orderType = 'purchase-order';

                                    await app.rpc('inventory.create-integration-order', payload);
                                } catch (error) {
                                    console.error('Fulfillment integration purchase order error ->', error.message);
                                }
                            })();
                        }

                        setTimeout(async () => {
                            try {
                                await app.rpc('inventory.transfers-sync-document-delivery-status', {
                                    transferId: id
                                });
                            } catch (error) {
                                console.error(error.message);
                            }
                        }, 3000);

                        // Run notification workflow.
                        if (app.hasModule('workflow') && data.status !== 'approved') {
                            const flowResult = await app.rpc(
                                'workflow.run-workflow',
                                {
                                    name: 'inventory.transfers',
                                    data: result,
                                    id,
                                    operation: 'update',
                                    actionTypes: ['notification']
                                },
                                {user: params.user}
                            );
                            data = flowResult.data;
                        }

                        return result;
                    }

                    // Canceled conditions.
                    // if (data.status === 'canceled') {
                    //     // Update reference documents.
                    //     if (!!oldTransfer.referenceId && !!oldTransfer.referenceCollection) {
                    //         const referenceId = oldTransfer.referenceId;
                    //         const referenceCollection = oldTransfer.referenceCollection;
                    //
                    //         const document = await app.collection(referenceCollection).findOne({
                    //             _id: referenceId,
                    //             $select: ['productsShippedWithInvoice', 'relatedDocuments']
                    //         });
                    //
                    //         // if (
                    //         //     referenceCollection === 'accounting.customer-invoices' ||
                    //         //     referenceCollection === 'accounting.vendor-invoices'
                    //         // ) {
                    //         //     if (!!document && !!document.productsShippedWithInvoice) {
                    //         //         throw new app.errors.Unprocessable(app.translate('To cancel this transfer, first cancel the relevant invoice!'));
                    //         //     }
                    //         // }
                    //
                    //         await app.collection(referenceCollection).patch(
                    //             {_id: referenceId},
                    //             {
                    //                 transferIds: [],
                    //                 relatedDocuments: document.relatedDocuments.filter(
                    //                     rd => rd.collection !== 'inventory.transfers'
                    //                 )
                    //             }
                    //         );
                    //     }
                    // }

                    // Reset error message for bulk operations
                    if (_.isString(data.bulkOperationError) && !_.isEmpty(data.bulkOperationError)) {
                        data.bulkOperationError = '';
                    }

                    // Save copied document record.
                    await saveCopiedDocumentRecords(app, id, data);

                    const result = await app
                        .collection('inventory.transfers')
                        .patch({_id: id}, data, {user: params.user});

                    // Run notification workflow.
                    if (app.hasModule('workflow') && data.status !== 'approved') {
                        const flowResult = await app.rpc(
                            'workflow.run-workflow',
                            {
                                name: 'inventory.transfers',
                                data: result,
                                id,
                                operation: 'update',
                                actionTypes: ['notification']
                            },
                            {user: params.user}
                        );
                        data = flowResult.data;
                    }

                    setTimeout(async () => {
                        try {
                            await app.rpc('inventory.transfers-sync-document-delivery-status', {
                                transferId: id
                            });
                        } catch (error) {
                            console.error(error.message);
                        }
                    }, 3000);

                    return result;
                } catch (error) {
                    throw error;
                }
            } else {
                // Check permission.
                await app.checkPermission({
                    user: params.user,
                    collection: 'inventory.transfers',
                    method: 'create'
                });

                // Run validation workflow.
                if (app.hasModule('workflow')) {
                    const flowResult = await app.rpc(
                        'workflow.run-workflow',
                        {
                            name: 'inventory.transfers',
                            data,
                            operation: 'create',
                            actionTypes: ['validation']
                        },
                        {user: params.user}
                    );
                    data = flowResult.data;
                }

                let result = await app.collection('inventory.transfers').create(data, {user: params.user});

                if (!!app.setting('inventory.useBulkEntryInTransfers')) {
                    try {
                        const transfer = result;
                        const operationType = await app.collection('inventory.operation-types').findOne({
                            _id: transfer.operationTypeId,
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        const warehouse = await app.collection('inventory.warehouses').findOne({
                            _id: operationType.warehouseId,
                            $select: ['shortName', 'name', 'branchId'],
                            $disableSoftDelete: true,
                            $disableActiveCheck: true
                        });
                        const transferOperations = [];
                        const subItemsOperations = [];

                        const products = await app.collection('inventory.products').find({
                            _id: {$in: _.uniq(transfer.items.map(item => item.productId))},
                            $select: [
                                'code',
                                'name',
                                'definition',
                                'displayName',
                                'baseUnitId',
                                'tracking',
                                'unitRatios',
                                'putawayStrategy',
                                'removalStrategy',
                                'warehouseManagement',
                                'negativeStock'
                            ],
                            $disableSoftDelete: true,
                            $disableActiveCheck: true
                        });
                        const productsMap = {};
                        for (const product of products) {
                            productsMap[product._id] = product;
                        }

                        for (const item of transfer.items ?? []) {
                            const product = productsMap[item.productId];
                            const baseUnit = await app.collection('kernel.units').findOne({
                                _id: product.baseUnitId,
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });

                            // Put-away strategy.
                            if (
                                !!app.setting('inventory.storageLocations') &&
                                operationType.type === 'incoming' &&
                                (product.putawayStrategy === 'first-location' ||
                                    product.putawayStrategy === 'last-location' ||
                                    product.putawayStrategy === 'default-location') &&
                                product.tracking !== 'serial' &&
                                product.tracking !== 'lot'
                            ) {
                                let destinationLocationId = null;

                                if (product.putawayStrategy === 'first-location') {
                                    const firstMove = await app.collection('inventory.moves').findOne({
                                        productId: product._id,
                                        warehouseId: warehouse._id,
                                        type: 'incoming',
                                        $sort: {date: 1},
                                        $select: ['destinationLocationId']
                                    });

                                    if (!!firstMove) {
                                        destinationLocationId = firstMove.destinationLocationId;
                                    }
                                } else if (product.putawayStrategy === 'last-location') {
                                    const lastMove = await app.collection('inventory.moves').findOne({
                                        productId: product._id,
                                        warehouseId: warehouse._id,
                                        type: 'incoming',
                                        $sort: {systemDate: -1},
                                        $select: ['destinationLocationId']
                                    });

                                    if (!!lastMove) {
                                        destinationLocationId = lastMove.destinationLocationId;
                                    }
                                } else if (product.putawayStrategy === 'default-location') {
                                    const w = (product.warehouseManagement ?? []).find(
                                        w => w.warehouseId === warehouse._id
                                    );

                                    if (!!w && !!w.defaultPutawayLocationId && !w.isBlocked) {
                                        destinationLocationId = w.defaultPutawayLocationId;
                                    }
                                }

                                if (!!destinationLocationId) {
                                    const sourceLocation = await app.collection('inventory.locations').findOne({
                                        _id: transfer.sourceLocationId,
                                        $disableActiveCheck: true,
                                        $disableSoftDelete: true
                                    });
                                    const destinationLocation = await app.collection('inventory.locations').findOne({
                                        _id: destinationLocationId,
                                        $disableActiveCheck: true,
                                        $disableSoftDelete: true
                                    });
                                    const ratio = (product.unitRatios || {})[item.unitId] || 1;
                                    let qty = ratio * item.requestedQty;

                                    const i = {};
                                    i.status = 'draft';
                                    i.id = Random.id(16);
                                    i.transferId = transfer._id;
                                    i.itemId = item.id;
                                    i.isValidated = false;
                                    i.productId = item.productId;
                                    i.productCode = item.productCode;
                                    i.productDefinition = item.productDefinition;
                                    i.barcode = item.barcode;
                                    i.sourceLocationId = sourceLocation._id;
                                    i.sourceLocationPath = sourceLocation.path;
                                    i.destinationLocationId = destinationLocation._id;
                                    i.destinationLocationPath = destinationLocation.path;
                                    i.acceptanceDate = app.datetime.local().toJSDate();
                                    i.productionDate = null;
                                    i.expirationDate = null;
                                    i.manufacturerWarrantyStartDate = null;
                                    i.manufacturerWarrantyEndDate = null;
                                    i.unitId = product.baseUnitId;
                                    i.unitName = baseUnit.name;
                                    i.qty = qty;

                                    subItemsOperations.push({
                                        insertOne: {document: i}
                                    });

                                    item.actualQty = item.requestedQty;
                                }
                            }

                            // Removal strategy.
                            if (
                                !!app.setting('inventory.storageLocations') &&
                                operationType.type === 'outgoing' &&
                                (product.removalStrategy === 'fifo' ||
                                    product.removalStrategy === 'lifo' ||
                                    product.removalStrategy === 'fefo')
                            ) {
                                const destinationLocation = await app.collection('inventory.locations').findOne({
                                    _id: transfer.destinationLocationId,
                                    $disableActiveCheck: true,
                                    $disableSoftDelete: true
                                });

                                // Get qty.
                                const ratio = (product.unitRatios || {})[item.unitId] || 1;
                                let requiredQty = ratio * item.requestedQty;

                                // Get quantities query.
                                const quantitiesQuery = {
                                    productId: product._id,
                                    warehouseId: warehouse._id,
                                    'location.type': 'internal',
                                    qty: {$gt: 0}
                                };
                                if (product.tracking === 'serial') {
                                    quantitiesQuery.$limit = requiredQty;
                                }
                                if (product.removalStrategy === 'fifo') {
                                    quantitiesQuery.$sort = {date: 1};
                                } else if (product.removalStrategy === 'lifo') {
                                    quantitiesQuery.$sort = {date: -1};
                                } else if (product.removalStrategy === 'fefo' && product.tracking === 'serial') {
                                    quantitiesQuery.$sort = {expirationDate: 1, date: 1};
                                }

                                // Get quantities.
                                const quantities = await app.collection('inventory.quantities').find(quantitiesQuery);

                                // Prepare sub items.
                                let currentQty = 0;
                                for (const quantity of quantities) {
                                    const sourceLocation = await app.collection('inventory.locations').findOne({
                                        _id: quantity.locationId,
                                        $disableActiveCheck: true,
                                        $disableSoftDelete: true
                                    });

                                    if (product.tracking === 'serial') {
                                        const serialNumber = await app.collection('inventory.serial-numbers').findOne({
                                            serialNumber: quantity.serialNumber
                                        });
                                        const existingItemCount = await app
                                            .collection('inventory.transfer-sub-items')
                                            .count({
                                                status: 'draft',
                                                serialNumber: serialNumber.serialNumber,
                                                sourceLocationId: sourceLocation._id
                                            });
                                        if (existingItemCount > 0) {
                                            continue;
                                        }

                                        const i = {};
                                        i.status = 'draft';
                                        i.id = Random.id(16);
                                        i.transferId = transfer._id;
                                        i.itemId = item.id;
                                        i.isValidated = false;
                                        i.productId = item.productId;
                                        i.productCode = item.productCode;
                                        i.productDefinition = item.productDefinition;
                                        i.barcode = item.barcode;
                                        i.sourceLocationId = sourceLocation._id;
                                        i.sourceLocationPath = sourceLocation.path;
                                        i.destinationLocationId = destinationLocation._id;
                                        i.destinationLocationPath = destinationLocation.path;
                                        i.serialNumber = serialNumber.serialNumber;
                                        i.lotNumber = serialNumber.lotNumber;
                                        i.acceptanceDate = serialNumber.acceptanceDate;
                                        i.productionDate = serialNumber.productionDate;
                                        i.expirationDate = serialNumber.expirationDate;
                                        i.manufacturerWarrantyStartDate = serialNumber.manufacturerWarrantyStartDate;
                                        i.manufacturerWarrantyEndDate = serialNumber.manufacturerWarrantyEndDate;
                                        i.unitId = product.baseUnitId;
                                        i.unitName = baseUnit.name;
                                        i.qty = 1;

                                        requiredQty -= i.qty;

                                        if (requiredQty < 0) {
                                            break;
                                        }

                                        currentQty += i.qty;

                                        subItemsOperations.push({
                                            insertOne: {document: i}
                                        });
                                    } else if (product.tracking === 'lot') {
                                        const lotNumber = await app.collection('inventory.lot-numbers').findOne({
                                            lotNumber: quantity.lotNumber
                                        });
                                        const existingItems = await app
                                            .collection('inventory.transfer-sub-items')
                                            .find({
                                                status: 'draft',
                                                productId: item.productId,
                                                lotNumber: lotNumber.lotNumber,
                                                sourceLocationId: sourceLocation._id
                                            });
                                        let availableQuantity = quantity.qty ?? 0;

                                        for (const existingItem of existingItems) {
                                            availableQuantity -= existingItem.qty ?? 0;
                                        }
                                        availableQuantity = Math.max(0, availableQuantity);

                                        if (!(availableQuantity > 0)) {
                                            continue;
                                        }

                                        const i = {};
                                        i.status = 'draft';
                                        i.id = Random.id(16);
                                        i.transferId = transfer._id;
                                        i.itemId = item.id;
                                        i.isValidated = false;
                                        i.productId = item.productId;
                                        i.productCode = item.productCode;
                                        i.productDefinition = item.productDefinition;
                                        i.barcode = item.barcode;
                                        i.sourceLocationId = sourceLocation._id;
                                        i.sourceLocationPath = sourceLocation.path;
                                        i.destinationLocationId = destinationLocation._id;
                                        i.destinationLocationPath = destinationLocation.path;
                                        i.lotNumber = lotNumber.lotNumber;
                                        i.acceptanceDate = lotNumber.acceptanceDate;
                                        i.productionDate = lotNumber.productionDate;
                                        i.expirationDate = lotNumber.expirationDate;
                                        i.manufacturerWarrantyStartDate = lotNumber.manufacturerWarrantyStartDate;
                                        i.manufacturerWarrantyEndDate = lotNumber.manufacturerWarrantyEndDate;
                                        i.unitId = product.baseUnitId;
                                        i.unitName = baseUnit.name;
                                        i.qty = 0;

                                        if (availableQuantity >= requiredQty) {
                                            i.qty = requiredQty;
                                        } else {
                                            i.qty = availableQuantity;
                                        }

                                        requiredQty -= i.qty;

                                        if (requiredQty < 0) {
                                            break;
                                        }

                                        if (i.qty === 0) {
                                            break;
                                        }

                                        currentQty += i.qty;

                                        subItemsOperations.push({
                                            insertOne: {document: i}
                                        });
                                    } else {
                                        const existingItems = await app
                                            .collection('inventory.transfer-sub-items')
                                            .find({
                                                status: 'draft',
                                                productId: item.productId,
                                                sourceLocationId: sourceLocation._id
                                            });
                                        let availableQuantity = quantity.qty ?? 0;

                                        for (const existingItem of existingItems) {
                                            availableQuantity -= existingItem.qty ?? 0;
                                        }
                                        availableQuantity = Math.max(0, availableQuantity);

                                        if (!(availableQuantity > 0)) {
                                            continue;
                                        }

                                        const i = {};
                                        i.status = 'draft';
                                        i.id = Random.id(16);
                                        i.transferId = transfer._id;
                                        i.itemId = item.id;
                                        i.isValidated = false;
                                        i.productId = item.productId;
                                        i.productCode = item.productCode;
                                        i.productDefinition = item.productDefinition;
                                        i.barcode = item.barcode;
                                        i.sourceLocationId = sourceLocation._id;
                                        i.sourceLocationPath = sourceLocation.path;
                                        i.destinationLocationId = destinationLocation._id;
                                        i.destinationLocationPath = destinationLocation.path;
                                        i.acceptanceDate = null;
                                        i.productionDate = null;
                                        i.expirationDate = null;
                                        i.manufacturerWarrantyStartDate = null;
                                        i.manufacturerWarrantyEndDate = null;
                                        i.unitId = product.baseUnitId;
                                        i.unitName = baseUnit.name;
                                        i.qty = 0;

                                        if (availableQuantity >= requiredQty) {
                                            i.qty = requiredQty;
                                        } else {
                                            i.qty = availableQuantity;
                                        }

                                        requiredQty -= i.qty;

                                        if (requiredQty < 0) {
                                            break;
                                        }

                                        if (i.qty === 0) {
                                            break;
                                        }

                                        currentQty += i.qty;

                                        subItemsOperations.push({
                                            insertOne: {document: i}
                                        });
                                    }
                                }

                                item.actualQty = currentQty / ratio;
                            }
                        }

                        let discount = 0;
                        let discountAmount = 0;
                        let subTotal = 0;
                        let subTotalAfterDiscount = 0;
                        let grandTotal = 0;
                        if (Array.isArray(transfer.items)) {
                            for (const row of transfer.items) {
                                subTotal += app.round((row.unitPrice || 0) * row.actualQty, 'total');
                                discountAmount += app.round(
                                    (((row.unitPrice || 0) * (row.discount || 0)) / 100) * row.actualQty,
                                    'total'
                                );
                            }
                        }
                        discount = subTotal > 0 ? (discountAmount / subTotal) * 100 : 0;
                        subTotalAfterDiscount = app.round(subTotal - discountAmount, 'total');
                        grandTotal = app.round(subTotalAfterDiscount, 'total');
                        transfer.subTotal = app.round(subTotal, 'total');
                        transfer.discountAmount = app.round(discountAmount, 'total');
                        transfer.subTotalAfterDiscount = subTotalAfterDiscount;
                        transfer.grandTotal = app.round(grandTotal, 'total');

                        transferOperations.push({
                            updateOne: {
                                filter: {_id: transfer._id},
                                update: {
                                    $set: {
                                        items: transfer.items,
                                        subTotal: transfer.subTotal,
                                        discountAmount: transfer.discountAmount,
                                        subTotalAfterDiscount: transfer.subTotalAfterDiscount,
                                        grandTotal: transfer.grandTotal
                                    }
                                }
                            }
                        });

                        if (subItemsOperations.length > 0) {
                            await app.collection('inventory.transfer-sub-items').bulkWrite(subItemsOperations);
                        }

                        if (transferOperations.length > 0) {
                            await app.collection('inventory.transfers').bulkWrite(transferOperations);
                        }

                        result = await app.collection('inventory.transfers').get(transfer._id);
                    } catch (error) {
                        console.error('Bulk entry automatic strategy error ->', error.message);
                    }
                }

                // Save copied document record.
                await saveCopiedDocumentRecords(app, result._id, data);

                // Run notification workflow.
                if (app.hasModule('workflow')) {
                    await app.rpc(
                        'workflow.run-workflow',
                        {
                            name: 'inventory.transfers',
                            data: result,
                            operation: 'create',
                            actionTypes: ['notification']
                        },
                        {user: params.user}
                    );
                }

                return result;
            }
        }
    },
    {
        name: 'transfers-create-shipping-order',
        async action(
            {
                transferId,
                carrierId,
                packageTypeId,
                cashOnDeliveryAmount,
                shippingPaymentType,
                packagingType,
                weight,
                volumetricWeight,
                deliveryAddress
            },
            params
        ) {
            const app = this.app;
            const transfer = await app.collection('inventory.transfers').get(transferId);
            const operationType = await app.collection('inventory.operation-types').get(transfer.operationTypeId);
            const warehouse = await app.collection('inventory.warehouses').get(operationType.warehouseId);
            const company = await app.collection('kernel.company').findOne({});
            const carrier = await app.collection('logistics.carriers').get(carrierId);
            const partner = await app.collection('kernel.partners').get(transfer.partnerId);
            const packageType = await app.collection('logistics.package-types').get(packageTypeId);
            const lengthUnit = await app.collection('kernel.units').findOne({
                category: 'length',
                symbol: 'm',
                $select: ['symbol']
            });
            const weightUnit = await app.collection('kernel.units').findOne({
                category: 'weight',
                symbol: 'kg',
                $select: ['symbol']
            });
            const round = n => app.roundNumber(n, 4);
            const hasSubItems = (transfer.items ?? []).some(
                item => Array.isArray(item.subItems) && item.subItems.length > 0
            );
            const order = {};

            let subItems = [];
            if (!!app.setting('inventory.useBulkEntryInTransfers') && !hasSubItems) {
                subItems = (
                    await app.collection('inventory.transfer-sub-items').find({
                        transferId
                    })
                ).map(subItem => {
                    subItem.quantity = subItem.qty;

                    return subItem;
                });
            } else {
                for (const item of transfer.items) {
                    for (const subItem of item.subItems || []) {
                        subItems.push({
                            ...subItem,
                            itemId: item.id
                        });
                    }
                }
            }

            order.type = 'normal';
            order.transferId = transferId;
            order.orderId = '';
            if (transfer.referenceCollection === 'sale.orders' && !!transfer.referenceId) {
                order.orderId = transfer.referenceId;
            }
            order.status = 'draft';
            order.code = await app.rpc('logistics.shipping-orders-get-code', {carrierId});
            if (!_.isEmpty(transfer.cargoTrackingCode)) {
                order.code = transfer.cargoTrackingCode;
            }
            order.carrierId = carrierId;
            order.carrierCode = carrier.code;
            order.carrierName = carrier.name;
            order.partnerId = transfer.partnerId;
            order.partnerIsCompany = partner.isCompany;
            order.partnerCode = partner.code;
            order.partnerName = partner.name;
            order.partnerTinIdentity = partner.isCompany ? partner.tin : partner.identity;
            order.partnerTaxDepartment = partner.taxDepartment;
            order.partnerEmail = partner.email;
            order.partnerPhone = partner.phone;
            order.currencyId = transfer.currencyId || company.currencyId;
            order.cashOnDeliveryAmount = cashOnDeliveryAmount;
            order.recordDate = app.datetime.local().toJSDate();
            order.issueDate = app.datetime.local().toJSDate();
            order.shippingPaymentType = shippingPaymentType;
            order.packagingType = packagingType;
            order.warehouseId = operationType.warehouseId;
            order.deliveryAddress = deliveryAddress;
            order.warehouseAddress = warehouse.address;
            order.integrationType = carrier.integrationType;
            order.integrationParams = carrier.integrationParams;
            order.volumetricWeightFactor = carrier.volumetricWeightFactor;
            order.items = [];
            if (
                !_.isPlainObject(order.deliveryAddress) ||
                !order.deliveryAddress.countryId ||
                !order.deliveryAddress.city ||
                !order.deliveryAddress.district ||
                !order.deliveryAddress.address
            ) {
                throw new app.errors.Unprocessable(this.translate('Delivery address is invalid!'));
            }
            if (
                !_.isPlainObject(order.warehouseAddress) ||
                !order.warehouseAddress.countryId ||
                !order.warehouseAddress.city ||
                !order.warehouseAddress.district ||
                !order.warehouseAddress.address
            ) {
                throw new app.errors.Unprocessable(this.translate('Warehouse address is invalid!'));
            }
            console.log('transfer.deliveryAddressId',transfer.deliveryAddressId)
            let deliveryContact = null;
            if (!!transfer.deliveryAddressId) {
                deliveryContact = await app.collection('kernel.contacts').findOne({
                    _id: transfer.deliveryAddressId
                });

                if (!!deliveryContact && !!deliveryContact.phone) {
                    order.partnerPhone = deliveryContact.phone;
                }
                if (!!deliveryContact && !!deliveryContact.name) {
                    order.partnerName = deliveryContact.name;
                }
            } else {
                deliveryContact = await app.collection('kernel.contacts').findOne({
                    partnerId: order.partnerId,
                    type: 'delivery-address',
                    'address.address': order.deliveryAddress.address
                });

                if (!!deliveryContact && !!deliveryContact.phone) {
                    order.partnerPhone = deliveryContact.phone;
                }
                if (!!deliveryContact && !!deliveryContact.name) {
                    order.partnerName = deliveryContact.name;
                }
            }

            const pkgNumbering = await app.collection('kernel.numbering').findOne({
                code: 'logisticsPackageNumbering',
                $select: ['_id'],
                $disableInUseCheck: true,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            let pkg = {};
            pkg.code = await app.rpc('kernel.common.request-number', {numberingId: pkgNumbering._id, save: true});
            pkg.packageTypeId = packageTypeId;
            pkg.carrierId = carrierId;
            pkg.partnerId = transfer.partnerId;
            pkg.barcode = generateRandomBarcode(16);
            pkg.recordDate = order.recordDate;
            pkg.isUsed = false;
            pkg.height = packageType.height;
            pkg.heightUnitId = packageType.heightUnitId;
            pkg.width = packageType.width;
            pkg.widthUnitId = packageType.widthUnitId;
            pkg.depth = packageType.depth;
            pkg.depthUnitId = packageType.depthUnitId;
            pkg.netWeight = packageType.defaultWeight;
            pkg.netWeightUnitId = packageType.defaultWeightUnitId;
            pkg.grossWeight = packageType.defaultWeight;
            pkg.grossWeightUnitId = packageType.defaultWeightUnitId;
            pkg.items = [];
            for (const transferItem of transfer.items || []) {
                const item = {};

                item.id = transferItem.id;
                item.transferId = transferId;
                item.transferCode = transfer.code;
                item.productId = transferItem.productId;
                item.productCode = transferItem.productCode;
                item.productDefinition = transferItem.productDefinition;
                item.barcode = transferItem.barcode;
                item.serialNumber = '';
                item.lotNumber = '';
                item.height = 0;
                item.heightUnitId = lengthUnit._id;
                item.heightUnitName = lengthUnit.symbol;
                item.width = 0;
                item.widthUnitId = lengthUnit._id;
                item.widthUnitName = lengthUnit.symbol;
                item.depth = 0;
                item.depthUnitId = lengthUnit._id;
                item.depthUnitName = lengthUnit.symbol;
                item.netWeight = 0;
                item.netWeightUnitId = weightUnit._id;
                item.netWeightUnitName = weightUnit.symbol;
                item.grossWeight = 0;
                item.grossWeightUnitId = weightUnit._id;
                item.grossWeightUnitName = weightUnit.symbol;
                item.unitId = transferItem.unitId;
                item.quantity = transferItem.actualQty;
                item.unitPrice = transferItem.unitPrice;

                pkg.items.push(item);

                // const itemSubItems = subItems.filter(subItem => subItem.itemId === transferItem.id);
                //
                // if (itemSubItems.length > 0) {
                //     for (const subItem of itemSubItems) {
                //         const item = {};
                //
                //         item.id = `${transferItem.id}${subItem.serialNumber}${subItem.lotNumber}`;
                //         item.transferId = transferId;
                //         item.transferCode = transfer.code;
                //         item.productId = transferItem.productId;
                //         item.productCode = transferItem.productCode;
                //         item.productDefinition = transferItem.productDefinition;
                //         item.barcode = transferItem.barcode;
                //         item.serialNumber = subItem.serialNumber;
                //         item.lotNumber = subItem.lotNumber;
                //         item.height = 0;
                //         item.heightUnitId = lengthUnit._id;
                //         item.heightUnitName = lengthUnit.symbol;
                //         item.width = 0;
                //         item.widthUnitId = lengthUnit._id;
                //         item.widthUnitName = lengthUnit.symbol;
                //         item.depth = 0;
                //         item.depthUnitId = lengthUnit._id;
                //         item.depthUnitName = lengthUnit.symbol;
                //         item.netWeight = 0;
                //         item.netWeightUnitId = weightUnit._id;
                //         item.netWeightUnitName = weightUnit.symbol;
                //         item.grossWeight = 0;
                //         item.grossWeightUnitId = weightUnit._id;
                //         item.grossWeightUnitName = weightUnit.symbol;
                //         item.unitId = transferItem.unitId;
                //         item.quantity = transferItem.requestedQty;
                //         item.unitPrice = transferItem.unitPrice;
                //
                //         pkg.items.push(item);
                //     }
                // } else {
                //     const item = {};
                //
                //     item.id = transferItem.id;
                //     item.transferId = transferId;
                //     item.transferCode = transfer.code;
                //     item.productId = transferItem.productId;
                //     item.productCode = transferItem.productCode;
                //     item.productDefinition = transferItem.productDefinition;
                //     item.barcode = transferItem.barcode;
                //     item.serialNumber = '';
                //     item.lotNumber = '';
                //     item.height = 0;
                //     item.heightUnitId = lengthUnit._id;
                //     item.heightUnitName = lengthUnit.symbol;
                //     item.width = 0;
                //     item.widthUnitId = lengthUnit._id;
                //     item.widthUnitName = lengthUnit.symbol;
                //     item.depth = 0;
                //     item.depthUnitId = lengthUnit._id;
                //     item.depthUnitName = lengthUnit.symbol;
                //     item.netWeight = 0;
                //     item.netWeightUnitId = weightUnit._id;
                //     item.netWeightUnitName = weightUnit.symbol;
                //     item.grossWeight = 0;
                //     item.grossWeightUnitId = weightUnit._id;
                //     item.grossWeightUnitName = weightUnit.symbol;
                //     item.unitId = transferItem.unitId;
                //     item.quantity = transferItem.actualQty;
                //     item.unitPrice = transferItem.unitPrice;
                //
                //     pkg.items.push(item);
                // }
            }
            const products = await app.collection('inventory.products').find({
                _id: {$in: _.uniq(pkg.items.map(item => item.productId))},
                $select: ['unitMeasurements'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            const productsMap = {};
            for (const product of products) {
                productsMap[product._id] = product;
            }
            pkg.items = pkg.items.map(item => {
                const product = productsMap[item.productId];
                const um = product.unitMeasurements.find(um => um.unitId === item.unitId);

                if (!!um) {
                    if (um.height && um.heightUnitId) {
                        item.height = um.height;
                        item.heightUnitId = um.heightUnitId;
                    }
                    if (um.width && um.widthUnitId) {
                        item.width = um.width;
                        item.widthUnitId = um.widthUnitId;
                    }
                    if (um.depth && um.depthUnitId) {
                        item.depth = um.depth;
                        item.depthUnitId = um.depthUnitId;
                    }
                    if (um.netWeight && um.netWeightUnitId) {
                        item.netWeight = um.netWeight;
                        item.netWeightUnitId = um.netWeightUnitId;
                    }
                    if (um.grossWeight && um.grossWeightUnitId) {
                        item.grossWeight = um.grossWeight;
                        item.grossWeightUnitId = um.grossWeightUnitId;
                    }
                }

                return item;
            });
            let unitIds = [];
            for (const item of pkg.items) {
                unitIds.push(
                    ...[
                        pkg.grossWeightUnitId,
                        pkg.widthUnitId,
                        pkg.heightUnitId,
                        pkg.depthUnitId,
                        item.unitId,
                        item.heightUnitId,
                        item.widthUnitId,
                        item.depthUnitId,
                        item.netWeightUnitId,
                        item.grossWeightUnitId
                    ]
                );
            }
            unitIds = _.uniq(unitIds);
            const units = await app.collection('kernel.units').find({
                _id: {$in: unitIds},
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            const unitsMap = {};
            for (const unit of units) {
                unitsMap[unit._id] = unit;
            }
            pkg.items = pkg.items.map(item => {
                item.unitName = unitsMap[item.unitId].name;
                item.heightUnitName = unitsMap[item.heightUnitId].symbol;
                item.widthUnitName = unitsMap[item.widthUnitId].symbol;
                item.depthUnitName = unitsMap[item.depthUnitId].symbol;
                item.netWeightUnitName = unitsMap[item.netWeightUnitId].symbol;
                item.grossWeightUnitName = unitsMap[item.grossWeightUnitId].symbol;

                return item;
            });
            pkg = await app.collection('logistics.packages').create(pkg, {user: params.user});

            const item = {};
            item.id = Random.id();
            item.packageId = pkg._id;
            item.barcode = pkg.barcode;
            item.description = `${order.code} - ${partner.name}`;
            item.quantity = 1;
            const widthUnit = unitsMap[pkg.widthUnitId];
            const heightUnit = unitsMap[pkg.heightUnitId];
            const depthUnit = unitsMap[pkg.depthUnitId];
            let width = 0;
            let height = 0;
            let depth = 0;
            if (widthUnit) {
                if (widthUnit.type === 'smaller') width = round(round(pkg.width) / round(widthUnit.ratio));
                else if (widthUnit.type === 'reference') width = round(pkg.width);
                else if (widthUnit.type === 'bigger') width = round(pkg.width) * round(widthUnit.ratio);
            }
            if (heightUnit) {
                if (heightUnit.type === 'smaller') height = round(round(pkg.height) / round(heightUnit.ratio));
                else if (heightUnit.type === 'reference') height = round(pkg.height);
                else if (heightUnit.type === 'bigger') height = round(pkg.height) * round(heightUnit.ratio);
            }
            if (depthUnit) {
                if (depthUnit.type === 'smaller') depth = round(round(pkg.depth) / round(depthUnit.ratio));
                else if (depthUnit.type === 'reference') depth = round(pkg.depth);
                else if (depthUnit.type === 'bigger') depth = round(pkg.depth) * round(depthUnit.ratio);
            }
            width = round(width * 100);
            height = round(height * 100);
            depth = round(depth * 100);
            item.width = width;
            item.height = height;
            item.depth = depth;
            item.weight = round(weight);
            item.volumetricWeight = round(volumetricWeight);
            order.items.push(item);

            order.products = pkg.items.map(item => {
                const product = {};

                product.id = item.id;
                product.productId = item.productId;
                product.productCode = item.productCode;
                product.productDefinition = item.productDefinition;
                product.unitId = item.unitId;
                product.unitName = item.unitName;
                product.quantity = item.quantity;
                product.packageCode = pkg.code;
                product.packageId = pkg._id;

                return product;
            });

            // E-commerce integration shipping order.
            if (
                app.hasModule('ecommerce') &&
                transfer.referenceCollection === 'sale.orders' &&
                !!transfer.referenceId
            ) {
                const saleOrder = await app.collection('sale.orders').findOne({
                    _id: transfer.referenceId,
                    $select: ['_id', 'storeId']
                });

                if (!!saleOrder && saleOrder.storeId) {
                    try {
                        const cargoTrackingCode = await ecommerceIntegrations.changeCarrier(
                            app,
                            saleOrder.storeId,
                            saleOrder._id,
                            carrierId
                        );

                        if (typeof cargoTrackingCode === 'string' && cargoTrackingCode.length > 0) {
                            order.code = cargoTrackingCode;
                        }
                    } catch (error) {
                        console.log(error.message);
                    }

                    try {
                        await ecommerceIntegrations.updateDeliveryInfo(app, saleOrder.storeId, {
                            orderId: saleOrder._id,
                            cargoTrackingCode: order.code,
                            carrierId,
                            boxQuantity: 1,
                            weight,
                            volumetricWeight: round(volumetricWeight)
                        });
                    } catch (error) {
                        console.log(error.message);
                    }
                }
            }

            const shippingOrder = await app.collection('logistics.shipping-orders').create(order, {user: params.user});

            try {
                await app.rpc('logistics.shipping-orders-approve', shippingOrder._id);
            } catch (error) {
                await app.collection('logistics.packages').bulkWrite([
                    {
                        deleteOne: {
                            filter: {_id: pkg._id}
                        }
                    }
                ]);
                await app.collection('logistics.shipping-orders').bulkWrite([
                    {
                        deleteOne: {
                            filter: {_id: shippingOrder._id}
                        }
                    }
                ]);

                throw error;
            }

            const transferPayload = {
                packageParams: {
                    packageIds: [pkg._id],
                    packageItems: pkg.items.map(item => {
                        item.packageId = pkg._id;

                        return item;
                    })
                },
                carrierId: order.carrierId,
                cargoTrackingCode: order.code,
                relatedDocuments: (transfer.relatedDocuments || [])
                    .filter(rd => rd.collection !== 'logistics.packages')
                    .filter(rd => rd.collection !== 'logistics.logistics.shipping-orders')
            };
            transferPayload.relatedDocuments.push({
                collection: 'logistics.packages',
                view: 'logistics.operations.packages',
                title: 'Packages',
                ids: [pkg._id]
            });
            transferPayload.relatedDocuments.push({
                collection: 'logistics.shipping-orders',
                view: 'logistics.operations.shipping-orders',
                title: 'Shipping Orders',
                ids: [shippingOrder._id]
            });
            await app.collection('inventory.transfers').bulkWrite([
                {
                    updateOne: {
                        filter: {_id: transferId},
                        update: {
                            $set: transferPayload
                        }
                    }
                }
            ]);

            if (!!order.orderId) {
                const saleOrder = await app.collection('sale.orders').findOne({
                    _id: order.orderId,
                    $select: ['relatedDocuments']
                });
                const saleOrderPayload = {
                    carrierId: order.carrierId,
                    cargoTrackingCode: order.code,
                    relatedDocuments: (saleOrder.relatedDocuments || []).filter(
                        rd => rd.collection !== 'logistics.logistics.shipping-orders'
                    )
                };
                saleOrderPayload.relatedDocuments.push({
                    collection: 'logistics.shipping-orders',
                    view: 'logistics.operations.shipping-orders',
                    title: 'Shipping Orders',
                    ids: [shippingOrder._id]
                });
                await app.collection('sale.orders').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: order.orderId},
                            update: {
                                $set: saleOrderPayload
                            }
                        }
                    }
                ]);
            }

            return shippingOrder;
        }
    },
    {
        name: 'transfers-item-get-source-locations',
        async action(payload, params) {
            const search = (payload.search || '').trim();
            const productId = payload.filters.productId;
            const parentPath = payload.filters.parentPath;
            const query = {
                ...(payload.filters.query || {}),
                productId,
                'location.type': 'internal',
                qty: {$gt: 0},
                $and: [
                    {
                        $or: [
                            {
                                'location.path': {
                                    $regex: `^${toUpper(escapeRegExp(parentPath))}`,
                                    $options: 'i'
                                }
                            },
                            {
                                'location.path': {
                                    $regex: `^${toLower(escapeRegExp(parentPath))}`,
                                    $options: 'i'
                                }
                            }
                        ]
                    }
                ]
            };
            const result = [];

            if (search) {
                query.$and.push({
                    $or: [
                        {
                            'location.path': {
                                $regex: `^${toUpper(escapeRegExp(search))}`,
                                $options: 'i'
                            }
                        },
                        {
                            'location.path': {
                                $regex: `^${toLower(escapeRegExp(search))}`,
                                $options: 'i'
                            }
                        }
                    ]
                });
            }

            if (payload.value) {
                const location = await this.app.collection('inventory.locations').findOne({
                    [payload.valueFrom]: payload.value,
                    $disableSoftDelete: true
                });
                const item = {
                    _id: location._id,
                    path: location.path,
                    stockQuantity: 0
                };

                result.push(item);
            }

            const rq = await this.app.collection('inventory.quantities').aggregate([
                {$match: query},
                {
                    $group: {
                        _id: '$locationId',
                        qty: {$sum: '$qty'},
                        path: {$first: '$location.path'}
                    }
                },
                {
                    $project: {
                        _id: 0,
                        locationId: '$_id',
                        qty: 1,
                        path: 1
                    }
                },
                {$sort: {createdAt: -1}},
                {$limit: 6}
            ]);

            for (const r of rq) {
                const existingIndex = _.findIndex(result, item => item._id === r.locationId);

                if (existingIndex !== -1) {
                    result[existingIndex].stockQuantity += r.qty;
                } else {
                    const item = {};

                    item._id = r.locationId;
                    item.path = r.path;
                    item.stockQuantity = r.qty;

                    result.push(item);
                }
            }

            return _.orderBy(result, ['stockQuantity'], ['desc']);
        }
    },
    {
        name: 'transfers-initialize-sub-items-for-barcode-entry',
        async action(payload, params) {
            const app = this.app;
            const storageLocations = app.setting('inventory.storageLocations');
            const {type, tracking, sourceLocationId, destinationLocationId, warehouseId, row, item} = payload;
            const subItems = Array.isArray(row.subItems) ? row.subItems : [];

            if (!storageLocations) {
                if (tracking === 'serial') {
                    for (const e of item.extra) {
                        if (!!e.serialNumber) {
                            subItems.push({
                                unitId: item.baseUnitId,
                                serialNumber: e.serialNumber,
                                lotNumber: e.lotNumber || '',
                                sourceLocationId,
                                destinationLocationId,
                                expirationDate: e.expirationDate || null,
                                quantity: 1
                            });
                        }
                    }
                } else if (tracking === 'lot') {
                    const lotMap = {};

                    for (const e of item.extra) {
                        if (!!e.lotNumber) {
                            if (!_.isNumber(lotMap[e.lotNumber])) {
                                lotMap[e.lotNumber] = 0;
                            }

                            if (_.isFinite(e.baseQuantity)) {
                                lotMap[e.lotNumber] += e.baseQuantity;
                            } else {
                                lotMap[e.lotNumber]++;
                            }
                        }
                    }

                    for (const lotNumber of Object.keys(lotMap)) {
                        const e = item.extra.find(e => e.lotNumber === lotNumber);

                        subItems.push({
                            unitId: row.baseUnitId,
                            lotNumber,
                            sourceLocationId,
                            destinationLocationId,
                            quantity: lotMap[lotNumber],
                            expirationDate: (e ?? {}).expirationDate || null
                        });
                    }
                } else {
                    subItems.push({
                        unitId: item.baseUnitId,
                        sourceLocationId,
                        destinationLocationId,
                        quantity: item.baseQuantity
                    });
                }
            } else {
                const product = await app.collection('inventory.products').findOne({
                    _id: item.productId,
                    $select: ['_id', 'putawayStrategy', 'removalStrategy', 'warehouseManagement'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                if (type === 'incoming') {
                    let finalSourceLocationId = null;
                    let finalDestinationLocationId = null;

                    if (product.putawayStrategy === 'first-location') {
                        const firstMove = await app.collection('inventory.moves').findOne({
                            productId: product._id,
                            warehouseId: warehouseId,
                            $sort: {date: 1},
                            $select: ['destinationLocationId']
                        });

                        if (!!firstMove) {
                            finalDestinationLocationId = firstMove.destinationLocationId;
                        } else {
                            finalDestinationLocationId = destinationLocationId;
                        }
                    } else if (product.putawayStrategy === 'last-location') {
                        const lastMove = await app.collection('inventory.moves').findOne({
                            productId: product._id,
                            warehouseId: warehouseId,
                            $sort: {date: -1},
                            $select: ['destinationLocationId']
                        });

                        if (!!lastMove) {
                            finalDestinationLocationId = lastMove.destinationLocationId;
                        } else {
                            finalDestinationLocationId = destinationLocationId;
                        }
                    } else if (product.putawayStrategy === 'default-location') {
                        const w = product.warehouseManagement.find(w => w.warehouseId === warehouseId);

                        if (!!w && !!w.defaultPutawayLocationId && !w.isBlocked) {
                            finalDestinationLocationId = w.defaultPutawayLocationId;
                        } else {
                            finalDestinationLocationId = destinationLocationId;
                        }
                    } else {
                        finalDestinationLocationId = destinationLocationId;
                    }

                    finalSourceLocationId = sourceLocationId;

                    if (tracking === 'serial') {
                        for (const e of item.extra) {
                            if (!!e.serialNumber) {
                                subItems.push({
                                    unitId: item.baseUnitId,
                                    serialNumber: e.serialNumber,
                                    lotNumber: e.lotNumber || '',
                                    sourceLocationId: finalSourceLocationId,
                                    destinationLocationId: destinationLocationId,
                                    expirationDate: e.expirationDate || null,
                                    quantity: 1
                                });
                            }
                        }
                    } else if (tracking === 'lot') {
                        const lotMap = {};

                        for (const e of item.extra) {
                            if (!!e.lotNumber) {
                                if (!_.isNumber(lotMap[e.lotNumber])) {
                                    lotMap[e.lotNumber] = 0;
                                }

                                if (_.isFinite(e.baseQuantity)) {
                                    lotMap[e.lotNumber] += e.baseQuantity;
                                } else {
                                    lotMap[e.lotNumber]++;
                                }
                            }
                        }

                        for (const lotNumber of Object.keys(lotMap)) {
                            const e = item.extra.find(e => e.lotNumber === lotNumber);

                            subItems.push({
                                unitId: row.baseUnitId,
                                lotNumber,
                                sourceLocationId: finalSourceLocationId,
                                destinationLocationId: destinationLocationId,
                                quantity: lotMap[lotNumber],
                                expirationDate: (e ?? {}).expirationDate || null
                            });
                        }
                    } else {
                        subItems.push({
                            unitId: item.baseUnitId,
                            sourceLocationId: finalSourceLocationId,
                            destinationLocationId: destinationLocationId,
                            quantity: item.baseQuantity
                        });
                    }
                } else {
                    if (tracking === 'serial') {
                        for (const e of item.extra) {
                            if (!e.serialNumber) {
                                continue;
                            }

                            const serial = await app.collection('inventory.serial-numbers').findOne({
                                serialNumber: e.serialNumber,
                                $select: ['locationId', 'expirationDate']
                            });

                            if (!!serial) {
                                subItems.push({
                                    unitId: item.baseUnitId,
                                    serialNumber: e.serialNumber,
                                    lotNumber: e.lotNumber,
                                    sourceLocationId: serial.locationId,
                                    destinationLocationId: destinationLocationId,
                                    expirationDate: serial.expirationDate,
                                    quantity: 1
                                });
                            }
                        }
                    } else if (tracking === 'lot') {
                        const lotMap = {};

                        for (const e of item.extra) {
                            if (!!e.lotNumber) {
                                if (!_.isNumber(lotMap[e.lotNumber])) {
                                    lotMap[e.lotNumber] = 0;
                                }

                                if (_.isFinite(e.baseQuantity)) {
                                    lotMap[e.lotNumber] += e.baseQuantity;
                                } else {
                                    lotMap[e.lotNumber]++;
                                }
                            }
                        }

                        for (const lotNumber of Object.keys(lotMap)) {
                            const e = item.extra.find(e => e.lotNumber === lotNumber);

                            const requiredQty = lotMap[lotNumber];
                            const quantitiesQuery = {
                                lotNumber,
                                'location.type': 'internal'
                            };
                            if (product.removalStrategy === 'fifo') {
                                quantitiesQuery.$sort = {date: 1};
                            } else if (product.removalStrategy === 'lifo') {
                                quantitiesQuery.$sort = {date: -1};
                            }

                            const quantities = await app.collection('inventory.quantities').find(quantitiesQuery);

                            let currentQty = 0;
                            for (const quantity of quantities) {
                                const subItem = {
                                    unitId: item.baseUnitId,
                                    serialNumber: '',
                                    lotNumber,
                                    sourceLocationId: quantity.locationId,
                                    destinationLocationId,
                                    quantity: 0,
                                    expirationDate: (e ?? {}).expirationDate || null
                                };

                                if (quantity.qty >= requiredQty) {
                                    subItem.quantity = requiredQty;
                                } else {
                                    subItem.quantity = quantity.qty;
                                }

                                currentQty += subItem.quantity;

                                let breakLoop = false;
                                if (currentQty > requiredQty) {
                                    const diff = currentQty - requiredQty;

                                    subItem.quantity -= diff;

                                    breakLoop = true;
                                }

                                subItems.push(subItem);

                                if (breakLoop) {
                                    break;
                                }

                                if (currentQty >= requiredQty) {
                                    break;
                                }
                            }
                        }
                    } else {
                        const requiredQty = item.baseQuantity;
                        const quantitiesQuery = {
                            productId: item.productId,
                            warehouseId,
                            'location.type': 'internal'
                        };
                        if (product.removalStrategy === 'fifo') {
                            quantitiesQuery.$sort = {date: 1};
                        } else if (product.removalStrategy === 'lifo') {
                            quantitiesQuery.$sort = {date: -1};
                        }
                        const quantities = await app.collection('inventory.quantities').find(quantitiesQuery);
                        let currentQty = 0;

                        for (const quantity of quantities) {
                            const subItem = {
                                unitId: item.baseUnitId,
                                sourceLocationId: quantity.locationId,
                                destinationLocationId,
                                quantity: 0
                            };

                            if (quantity.qty >= requiredQty) {
                                subItem.quantity = requiredQty;
                            } else {
                                subItem.quantity = quantity.qty;
                            }

                            currentQty += subItem.quantity;

                            let breakLoop = false;
                            if (currentQty > requiredQty) {
                                const diff = currentQty - requiredQty;

                                subItem.quantity -= diff;

                                breakLoop = true;
                            }
                            if (!(subItem.quantity > 0)) {
                                break;
                            }

                            subItems.push(subItem);

                            if (breakLoop) {
                                break;
                            }

                            if (currentQty >= requiredQty) {
                                break;
                            }
                        }
                    }
                }
            }

            return subItems;
        }
    },
    {
        name: 'transfers-save-package-params',
        async action({transferId, packageIds, packageItems}, params) {
            const app = this.app;
            const packageOperations = [];
            const filteredPackageIds = [];

            for (const packageId of packageIds) {
                const pack = await app.collection('logistics.packages').findOne({
                    _id: packageId,
                    $select: ['items']
                });
                const filteredPackageItems = packageItems.filter(i => i.packageId === packageId);

                if (filteredPackageItems.length > 0) {
                    const items = (pack.items || [])
                        .filter(i => i.transferId !== transferId)
                        .concat(filteredPackageItems);

                    filteredPackageIds.push(packageId);

                    packageOperations.push({
                        updateOne: {
                            filter: {_id: packageId},
                            update: {
                                $set: {
                                    items
                                }
                            }
                        }
                    });
                }
            }

            if (packageOperations.length > 0) {
                await app.collection('logistics.packages').bulkWrite(packageOperations);
            }

            const transfer = await app.collection('inventory.transfers').findOne({
                _id: transferId,
                $select: ['relatedDocuments']
            });
            const transferPayload = {
                packageParams: {packageIds, packageItems},
                relatedDocuments: (transfer.relatedDocuments || []).filter(rd => rd.collection !== 'logistics.packages')
            };

            if (filteredPackageIds.length > 0) {
                transferPayload.relatedDocuments.push({
                    collection: 'logistics.packages',
                    view: 'logistics.operations.packages',
                    title: 'Packages',
                    ids: filteredPackageIds
                });
            }

            await app.collection('inventory.transfers').bulkWrite([
                {
                    updateOne: {
                        filter: {_id: transferId},
                        update: {
                            $set: transferPayload
                        }
                    }
                }
            ]);
        }
    },
    {
        name: 'transfers-change-document-no',
        async action(payload, params) {
            const app = this.app;
            const {transferId, recordDate, documentNo} = payload;

            const journalEntry = await app.collection('accounting.journal-entries').findOne({
                recordDate: {$gte: recordDate},
                'relatedDocuments.ids': transferId,
                $select: ['_id']
            });
            if (!!journalEntry) {
                await app.collection('accounting.journal-entries').patch(
                    {_id: journalEntry._id},
                    {
                        documentNo: documentNo
                    }
                );
                await app.collection('accounting.transactions').patch(
                    {entryId: journalEntry._id},
                    {
                        documentNo: documentNo
                    }
                );
            }
            await app.collection('inventory.transfers').bulkWrite([
                {
                    updateOne: {
                        filter: {_id: transferId},
                        update: {
                            $set: {documentNo}
                        }
                    }
                }
            ]);
        }
    },
    {
        name: 'transfers-check-items-availability',
        async action({type, selectedSourceLocation, selectedDestinationLocation, model}, params) {
            const app = this.app;
            const result = [];

            for (const row of model.items || []) {
                let report = [];
                if (type === 'incoming') {
                    report = await app.rpc('inventory.get-stock-report', {
                        ...(!!model.referenceId
                            ? {
                                  externalReservationsQuery: {
                                      documentId: {
                                          $ne: model.referenceId
                                      }
                                  }
                              }
                            : {}),
                        date: model.scheduledDate || app.datetime.local().toJSDate(),
                        productId: row.productId,
                        query: {
                            $or: [
                                {
                                    'location.path': {
                                        $regex: `^${toUpper(escapeRegExp(selectedDestinationLocation.path))}`,
                                        $options: 'i'
                                    }
                                },
                                {
                                    'location.path': {
                                        $regex: `^${toLower(escapeRegExp(selectedDestinationLocation.path))}`,
                                        $options: 'i'
                                    }
                                }
                            ]
                        },
                        ...(app.hasModule('pcm') && !!row.pcmHash ? {pcmHash: row.pcmHash} : {})
                    });
                } else if (type === 'internal') {
                    report = await app.rpc('inventory.get-stock-report', {
                        ...(!!model.referenceId
                            ? {
                                  externalReservationsQuery: {
                                      documentId: {
                                          $ne: model.referenceId
                                      }
                                  }
                              }
                            : {}),
                        date: model.scheduledDate || app.datetime.local().toJSDate(),
                        productId: row.productId,
                        query: {
                            $or: [
                                {
                                    'location.path': {
                                        $regex: `^${toUpper(escapeRegExp(selectedSourceLocation.path))}`,
                                        $options: 'i'
                                    }
                                },
                                {
                                    'location.path': {
                                        $regex: `^${toLower(escapeRegExp(selectedSourceLocation.path))}`,
                                        $options: 'i'
                                    }
                                }
                            ]
                        },
                        ...(app.hasModule('pcm') && !!row.pcmHash ? {pcmHash: row.pcmHash} : {})
                    });
                } else if (type === 'outgoing') {
                    report = await app.rpc('inventory.get-stock-report', {
                        ...(!!model.referenceId
                            ? {
                                  externalReservationsQuery: {
                                      documentId: {
                                          $ne: model.referenceId
                                      }
                                  }
                              }
                            : {}),
                        date: model.scheduledDate || app.datetime.local().toJSDate(),
                        productId: row.productId,
                        query: {
                            $or: [
                                {
                                    'location.path': {
                                        $regex: `^${toUpper(escapeRegExp(selectedSourceLocation.path))}`,
                                        $options: 'i'
                                    }
                                },
                                {
                                    'location.path': {
                                        $regex: `^${toLower(escapeRegExp(selectedSourceLocation.path))}`,
                                        $options: 'i'
                                    }
                                }
                            ]
                        },
                        ...(app.hasModule('pcm') && !!row.pcmHash ? {pcmHash: row.pcmHash} : {})
                    });
                }
                if (report.length > 0) {
                    const r = report[0];

                    row.stockQuantity = r.stockQuantity;
                    row.orderedQuantity = r.orderedQuantity;
                    row.assignedQuantity = r.assignedQuantity;
                    row.availableQuantity = r.availableQuantity;
                } else {
                    row.stockQuantity = 0;
                    row.orderedQuantity = 0;
                    row.assignedQuantity = 0;
                    row.availableQuantity = 0;
                }

                result.push(row);
            }

            return result;
        }
    },
    {
        name: 'transfers-print-items',
        async action(transferId, params) {
            const app = this.app;
            const transfer = await app.collection('inventory.transfers').get(transferId);
            const round = n => app.roundNumber(n, 2);
            const t = text => app.translate(text);
            const products = await app.collection('inventory.products').find({
                _id: {$in: (transfer.items || []).map(i => i.productId)},
                $select: ['displayName', 'barcode'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            const productsMap = {};
            for (const product of products) {
                productsMap[product._id] = product;
            }
            const units = await app.collection('kernel.units').find({
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            const unitsMap = {};
            for (const unit of units) {
                unitsMap[unit._id] = unit;
            }
            const hasSubItems = (transfer.items ?? []).some(
                item => Array.isArray(item.subItems) && item.subItems.length > 0
            );

            let subItems = [];
            if (!!app.setting('inventory.useBulkEntryInTransfers') && !hasSubItems) {
                subItems = (
                    await app.collection('inventory.transfer-sub-items').find({
                        transferId
                    })
                ).map(subItem => {
                    subItem.quantity = subItem.qty;

                    return subItem;
                });
            } else {
                for (const item of transfer.items) {
                    for (const subItem of item.subItems || []) {
                        subItems.push({
                            ...subItem,
                            itemId: item.id
                        });
                    }
                }
            }

            let locationIds = [];
            let locationsMap = {};
            for (const subItem of subItems) {
                if (!!subItem.sourceLocationId) {
                    locationIds.push(subItem.sourceLocationId);
                }
                if (!!subItem.destinationLocationId) {
                    locationIds.push(subItem.destinationLocationId);
                }
            }
            locationIds = _.uniq(locationIds);
            if (locationIds.length > 0) {
                const locations = await app.collection('inventory.locations').find({
                    _id: {$in: locationIds},
                    $select: ['_id', 'path'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                for (const location of locations) {
                    locationsMap[location._id] = location;
                }
            }

            const itemsTable = {
                table: {
                    headerRows: 1,
                    dontBreakRows: true,
                    widths: ['*', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto'],
                    body: []
                },
                layout: {
                    hLineWidth(i, node) {
                        return i === 1 ? 1 : 0.5;
                    },
                    vLineWidth(i, node) {
                        return 0.5;
                    },
                    hLineColor(i, node) {
                        return '#bdc3c7';
                    },
                    vLineColor(i, node) {
                        return '#bdc3c7';
                    },
                    fillColor(rowIndex, node, columnIndex) {
                        return rowIndex % 2 === 1 ? '#fcfcfc' : null;
                    }
                }
            };
            itemsTable.table.body.push([
                {
                    text: t('Product'),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                },
                {
                    text: t('Barcode'),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                },
                {
                    text: firstUpperAll(t('Serial number')),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                },
                {
                    text: firstUpperAll(t('Lot number')),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                },
                {
                    text: t('Location'),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                },
                {
                    text: t('Unit'),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                },
                {
                    text: t('Quantity'),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                }
            ]);

            for (const item of transfer.items || []) {
                const product = productsMap[item.productId];
                const itemSubItems = subItems.filter(subItem => subItem.itemId === item.id);

                if (itemSubItems.length > 0) {
                    for (const subItem of itemSubItems) {
                        const unit = unitsMap[subItem.unitId];
                        let location = null;
                        if (transfer.type === 'incoming') {
                            location = locationsMap[subItem.destinationLocationId];
                        } else {
                            location = locationsMap[subItem.sourceLocationId];
                        }

                        itemsTable.table.body.push([
                            {
                                text: product.displayName,
                                style: 'itemText',
                                border: [true, false, true, true]
                            },
                            {
                                text: item.barcode || '',
                                style: 'itemText',
                                border: [false, false, true, true],
                                noWrap: true
                            },
                            {
                                text: subItem.serialNumber,
                                style: 'itemText',
                                border: [false, false, true, true],
                                noWrap: true
                            },
                            {
                                text: subItem.lotNumber,
                                style: 'itemNumber',
                                border: [false, false, true, true],
                                noWrap: true
                            },
                            {
                                text: (location || {}).path || '',
                                style: 'itemNumber',
                                border: [false, false, true, true],
                                noWrap: true
                            },
                            {
                                text: unit.name,
                                style: 'itemNumber',
                                border: [false, false, true, true],
                                noWrap: true
                            },
                            {
                                text: round(subItem.quantity),
                                style: 'itemNumber',
                                border: [false, false, true, true],
                                noWrap: true
                            }
                        ]);
                    }
                } else {
                    const unit = unitsMap[item.unitId];

                    itemsTable.table.body.push([
                        {
                            text: product.displayName,
                            style: 'itemText',
                            border: [true, false, true, true]
                        },
                        {
                            text: item.barcode || '',
                            style: 'itemText',
                            border: [false, false, true, true],
                            noWrap: true
                        },
                        {
                            text: '',
                            style: 'itemText',
                            border: [false, false, true, true],
                            noWrap: true
                        },
                        {
                            text: '',
                            style: 'itemNumber',
                            border: [false, false, true, true],
                            noWrap: true
                        },
                        {
                            text: '',
                            style: 'itemNumber',
                            border: [false, false, true, true],
                            noWrap: true
                        },
                        {
                            text: unit.name,
                            style: 'itemNumber',
                            border: [false, false, true, true],
                            noWrap: true
                        },
                        {
                            text: round(item.actualQty),
                            style: 'itemNumber',
                            border: [false, false, true, true],
                            noWrap: true
                        }
                    ]);
                }
            }

            const payload = {};
            payload.title = `${t('Transfer Items')} ${transfer.code}`;
            if (transfer.type === 'incoming') {
                payload.title = `${t('Goods Receipt Items')} ${transfer.code}`;
            } else if (transfer.type === 'outgoing') {
                payload.title = `${t('Delivery Order Items')} ${transfer.code}`;
            }
            payload.definition = {
                content: [itemsTable],
                styles: {
                    // Items Header
                    itemsHeader: {
                        margin: 1,
                        fontSize: 8,
                        bold: true
                    },

                    // Item.
                    itemNumber: {
                        fontSize: 7,
                        margin: 2,
                        alignment: 'right'
                    },
                    itemText: {
                        fontSize: 7,
                        margin: 1
                    },
                    itemTotal: {
                        fontSize: 7,
                        margin: 2,
                        bold: true,
                        alignment: 'right'
                    },
                    itemTotalHeader: {
                        fontSize: 7,
                        margin: 2,
                        bold: true,
                        alignment: 'left'
                    }
                }
            };

            return app.print(payload);
        }
    },
    {
        name: 'transfers-print-product-locations',
        async action(transferId, params) {
            const app = this.app;
            const transfer = await app.collection('inventory.transfers').get(transferId);
            const round = n => app.roundNumber(n, 2);
            const t = text => app.translate(text);
            const productIds = _.uniq(transfer.items || []).map(i => i.productId);
            const products = await app.collection('inventory.products').find({
                _id: {$in: productIds},
                $select: ['displayName', 'barcode', 'baseUnitId'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            const productsMap = {};
            for (const product of products) {
                productsMap[product._id] = product;
            }
            const units = await app.collection('kernel.units').find({
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            const unitsMap = {};
            for (const unit of units) {
                unitsMap[unit._id] = unit;
            }
            const hasSubItems = (transfer.items ?? []).some(
                item => Array.isArray(item.subItems) && item.subItems.length > 0
            );

            let subItems = [];
            if (!!app.setting('inventory.useBulkEntryInTransfers') && !hasSubItems) {
                subItems = (
                    await app.collection('inventory.transfer-sub-items').find({
                        transferId
                    })
                ).map(subItem => {
                    subItem.quantity = subItem.qty;

                    return subItem;
                });
            } else {
                for (const item of transfer.items) {
                    for (const subItem of item.subItems || []) {
                        subItems.push({
                            ...subItem,
                            itemId: item.id
                        });
                    }
                }
            }

            let locationIds = [];
            let locationsMap = {};
            for (const subItem of subItems) {
                if (!!subItem.sourceLocationId) {
                    locationIds.push(subItem.sourceLocationId);
                }
                if (!!subItem.destinationLocationId) {
                    locationIds.push(subItem.destinationLocationId);
                }
            }
            locationIds = _.uniq(locationIds);
            if (locationIds.length > 0) {
                const locations = await app.collection('inventory.locations').find({
                    _id: {$in: locationIds},
                    $select: ['_id', 'path'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                for (const location of locations) {
                    locationsMap[location._id] = location;
                }
            }
            const operationType = await app.collection('inventory.operation-types').get(transfer.operationTypeId);

            const report = await app.collection('inventory.quantities').aggregate([
                {
                    $match: {
                        'location.type': 'internal',
                        warehouseId: operationType.warehouseId,
                        productId: {$in: productIds}
                    }
                },
                {
                    $group: {
                        _id: {productId: '$productId', locationPath: '$location.path'},
                        qty: {$sum: '$qty'}
                    }
                }
            ]);

            const itemsTable = {
                table: {
                    headerRows: 1,
                    dontBreakRows: true,
                    widths: ['*', 'auto', 'auto', 'auto', 'auto'],
                    body: []
                },
                layout: {
                    hLineWidth(i, node) {
                        return i === 1 ? 1 : 0.5;
                    },
                    vLineWidth(i, node) {
                        return 0.5;
                    },
                    hLineColor(i, node) {
                        return '#bdc3c7';
                    },
                    vLineColor(i, node) {
                        return '#bdc3c7';
                    },
                    fillColor(rowIndex, node, columnIndex) {
                        return rowIndex % 2 === 1 ? '#fcfcfc' : null;
                    }
                }
            };
            itemsTable.table.body.push([
                {
                    text: t('Product'),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                },
                {
                    text: t('Barcode'),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                },
                {
                    text: t('Location'),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                },
                {
                    text: t('Unit'),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                },
                {
                    text: t('Quantity'),
                    style: 'itemsHeader',
                    border: [false, false, false, true],
                    noWrap: true
                }
            ]);

            for (const item of transfer.items || []) {
                const product = productsMap[item.productId];
                const r = report.find(r => r._id.productId === item.productId);

                if (!product || !r) {
                    continue;
                }

                const unit = unitsMap[product.baseUnitId];

                itemsTable.table.body.push([
                    {
                        text: product.displayName,
                        style: 'itemText',
                        border: [true, false, true, true]
                    },
                    {
                        text: item.barcode || '',
                        style: 'itemText',
                        border: [false, false, true, true],
                        noWrap: true
                    },
                    {
                        text: r._id.locationPath,
                        style: 'itemNumber',
                        border: [false, false, true, true],
                        noWrap: true
                    },
                    {
                        text: unit.name,
                        style: 'itemNumber',
                        border: [false, false, true, true],
                        noWrap: true
                    },
                    {
                        text: round(r.qty),
                        style: 'itemNumber',
                        border: [false, false, true, true],
                        noWrap: true
                    }
                ]);
            }

            const payload = {};
            payload.title = t('Product Locations');
            payload.definition = {
                content: [itemsTable],
                styles: {
                    // Items Header
                    itemsHeader: {
                        margin: 1,
                        fontSize: 8,
                        bold: true
                    },

                    // Item.
                    itemNumber: {
                        fontSize: 7,
                        margin: 2,
                        alignment: 'right'
                    },
                    itemText: {
                        fontSize: 7,
                        margin: 1
                    },
                    itemTotal: {
                        fontSize: 7,
                        margin: 2,
                        bold: true,
                        alignment: 'right'
                    },
                    itemTotalHeader: {
                        fontSize: 7,
                        margin: 2,
                        bold: true,
                        alignment: 'left'
                    }
                }
            };

            return app.print(payload);
        }
    },
    {
        name: 'transfers-sync-document-delivery-status',
        async action(payload, params) {
            const app = this.app;
            const transferId = payload.transferId;
            const transfer = await app.collection('inventory.transfers').findOne({
                _id: transferId,
                $select: [
                    '_id',
                    'code',
                    'scheduledDate',
                    'documentNo',
                    'status',
                    'referenceCollection',
                    'relatedDocuments'
                ]
            });

            const relatedTransferId = (transfer.relatedDocuments || []).find(
                rd => rd.collection === 'inventory.transfers'
            )?.ids?.[0];

            let document = await app.collection('sale.orders').findOne({
                transferIds: transfer._id,
                $select: ['_id', 'transferIds', 'deliveries']
            });
            if (!document && relatedTransferId) {
                document = await app.collection('sale.orders').findOne({
                    transferIds: relatedTransferId,
                    $select: ['_id', 'transferIds', 'deliveries']
                });
            }
            if (!!document) {
                const deliveries = document.deliveries ?? [];
                const index = deliveries.findIndex(delivery => delivery.deliveryId === transfer._id);
                let deliveryStatus = 'waiting';

                if (index === -1) {
                    const delivery = {};

                    delivery.deliveryId = transfer._id;
                    delivery.deliveryCode = transfer.code;
                    delivery.deliveryDate = transfer.scheduledDate;
                    delivery.deliveryDocumentNo = transfer.documentNo;
                    delivery.deliveryStatus = !!payload.status ? payload.status : transfer.status;

                    deliveries.push(delivery);
                } else {
                    deliveries[index].deliveryCode = transfer.code;
                    deliveries[index].deliveryDate = transfer.scheduledDate;
                    deliveries[index].deliveryDocumentNo = transfer.documentNo;
                    deliveries[index].deliveryStatus = !!payload.status ? payload.status : transfer.status;
                }

                if (deliveries.every(delivery => delivery.deliveryStatus === 'approved')) {
                    deliveryStatus = 'delivered';

                    const hasReservations =
                        (await app.collection('inventory.external-reservations').count({
                            documentId: document._id,
                            $or: [{assigned: {$gt: 0}}, {ordered: {$gt: 0}}]
                        })) > 0;
                    if (hasReservations) {
                        deliveryStatus = 'partially-delivered';
                    }
                } else if (deliveries.some(delivery => delivery.deliveryStatus === 'approved')) {
                    deliveryStatus = 'partially-delivered';
                } else if (deliveries.every(delivery => delivery.deliveryStatus === 'canceled')) {
                    deliveryStatus = 'canceled';
                }

                await app.collection('sale.orders').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: document._id},
                            update: {$set: {deliveries, deliveryStatus}}
                        }
                    }
                ]);
            }

            document = await app.collection('purchase.orders').findOne({
                transferIds: transfer._id,
                $select: ['_id', 'transferIds', 'deliveries']
            });
            if (!document && relatedTransferId) {
                document = await app.collection('purchase.orders').findOne({
                    transferIds: relatedTransferId,
                    $select: ['_id', 'transferIds', 'deliveries']
                });
            }
            if (!!document) {
                const deliveries = document.deliveries ?? [];
                const index = deliveries.findIndex(delivery => delivery.deliveryId === transfer._id);
                let deliveryStatus = 'waiting';

                if (index === -1) {
                    const delivery = {};

                    delivery.deliveryId = transfer._id;
                    delivery.deliveryCode = transfer.code;
                    delivery.deliveryDate = transfer.scheduledDate;
                    delivery.deliveryDocumentNo = transfer.documentNo;
                    delivery.deliveryStatus = !!payload.status ? payload.status : transfer.status;

                    deliveries.push(delivery);
                } else {
                    deliveries[index].deliveryCode = transfer.code;
                    deliveries[index].deliveryDate = transfer.scheduledDate;
                    deliveries[index].deliveryDocumentNo = transfer.documentNo;
                    deliveries[index].deliveryStatus = !!payload.status ? payload.status : transfer.status;
                }

                if (deliveries.every(delivery => delivery.deliveryStatus === 'approved')) {
                    deliveryStatus = 'delivered';

                    const hasReservations =
                        (await app.collection('inventory.external-reservations').count({
                            documentId: document._id,
                            $or: [{assigned: {$gt: 0}}, {ordered: {$gt: 0}}]
                        })) > 0;
                    if (hasReservations) {
                        deliveryStatus = 'partially-delivered';
                    }
                } else if (deliveries.some(delivery => delivery.deliveryStatus === 'approved')) {
                    deliveryStatus = 'partially-delivered';
                } else if (deliveries.every(delivery => delivery.deliveryStatus === 'canceled')) {
                    deliveryStatus = 'canceled';
                }

                await app.collection('purchase.orders').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: document._id},
                            update: {$set: {deliveries, deliveryStatus}}
                        }
                    }
                ]);
            }

            document = await app.collection('accounting.customer-invoices').findOne({
                transferIds: transfer._id,
                $select: ['_id', 'transferIds', 'deliveries', 'relatedDocuments']
            });
            if (!document && relatedTransferId) {
                document = await app.collection('accounting.customer-invoices').findOne({
                    transferIds: relatedTransferId,
                    $select: ['_id', 'transferIds', 'deliveries', 'relatedDocuments']
                });
            }
            if (!!document) {
                const deliveries = document.deliveries ?? [];
                const index = deliveries.findIndex(delivery => delivery.deliveryId === transfer._id);
                let deliveryStatus = 'waiting';

                if (index === -1) {
                    const delivery = {};

                    delivery.deliveryId = transfer._id;
                    delivery.deliveryCode = transfer.code;
                    delivery.deliveryDate = transfer.scheduledDate;
                    delivery.deliveryDocumentNo = transfer.documentNo;
                    delivery.deliveryStatus = !!payload.status ? payload.status : transfer.status;

                    deliveries.push(delivery);
                } else {
                    deliveries[index].deliveryCode = transfer.code;
                    deliveries[index].deliveryDate = transfer.scheduledDate;
                    deliveries[index].deliveryDocumentNo = transfer.documentNo;
                    deliveries[index].deliveryStatus = !!payload.status ? payload.status : transfer.status;
                }

                if (deliveries.every(delivery => delivery.deliveryStatus === 'approved')) {
                    deliveryStatus = 'delivered';

                    const hasReservations =
                        (await app.collection('inventory.external-reservations').count({
                            documentId: document._id,
                            $or: [{assigned: {$gt: 0}}, {ordered: {$gt: 0}}]
                        })) > 0;
                    if (hasReservations) {
                        deliveryStatus = 'partially-delivered';
                    }
                } else if (deliveries.some(delivery => delivery.deliveryStatus === 'approved')) {
                    deliveryStatus = 'partially-delivered';
                } else if (deliveries.every(delivery => delivery.deliveryStatus === 'canceled')) {
                    deliveryStatus = 'canceled';
                }

                await app.collection('accounting.customer-invoices').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: document._id},
                            update: {$set: {deliveries, deliveryStatus}}
                        }
                    }
                ]);

                if (
                    transfer.referenceCollection === 'accounting.customer-invoices' &&
                    Array.isArray(document.relatedDocuments) &&
                    document.relatedDocuments.length > 0
                ) {
                    const saleOrderDocument = document.relatedDocuments.find(rd => rd.collection === 'sale.orders');

                    if (saleOrderDocument && Array.isArray(saleOrderDocument.ids) && saleOrderDocument.ids.length > 0) {
                        const order = await app.collection('sale.orders').findOne({
                            _id: saleOrderDocument.ids[0],
                            $select: ['_id', 'transferIds', 'deliveries'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });

                        if (!!order) {
                            const deliveries = order.deliveries ?? [];
                            const index = deliveries.findIndex(delivery => delivery.deliveryId === transfer._id);
                            let deliveryStatus = 'waiting';

                            if (index === -1) {
                                const delivery = {};

                                delivery.deliveryId = transfer._id;
                                delivery.deliveryCode = transfer.code;
                                delivery.deliveryDate = transfer.scheduledDate;
                                delivery.deliveryDocumentNo = transfer.documentNo;
                                delivery.deliveryStatus = !!payload.status ? payload.status : transfer.status;

                                deliveries.push(delivery);
                            } else {
                                deliveries[index].deliveryCode = transfer.code;
                                deliveries[index].deliveryDate = transfer.scheduledDate;
                                deliveries[index].deliveryDocumentNo = transfer.documentNo;
                                deliveries[index].deliveryStatus = !!payload.status ? payload.status : transfer.status;
                            }

                            if (deliveries.every(delivery => delivery.deliveryStatus === 'approved')) {
                                deliveryStatus = 'delivered';

                                const hasReservations =
                                    (await app.collection('inventory.external-reservations').count({
                                        documentId: order._id,
                                        $or: [{assigned: {$gt: 0}}, {ordered: {$gt: 0}}]
                                    })) > 0;
                                if (hasReservations) {
                                    deliveryStatus = 'partially-delivered';
                                }
                            } else if (deliveries.some(delivery => delivery.deliveryStatus === 'approved')) {
                                deliveryStatus = 'partially-delivered';
                            } else if (deliveries.every(delivery => delivery.deliveryStatus === 'canceled')) {
                                deliveryStatus = 'canceled';
                            }

                            await app.collection('sale.orders').bulkWrite([
                                {
                                    updateOne: {
                                        filter: {_id: order._id},
                                        update: {$set: {deliveries, deliveryStatus}}
                                    }
                                }
                            ]);
                        }
                    }
                }
            }

            document = await app.collection('accounting.vendor-invoices').findOne({
                transferIds: transfer._id,
                $select: ['_id', 'transferIds', 'deliveries', 'relatedDocuments']
            });
            if (!document && relatedTransferId) {
                document = await app.collection('accounting.vendor-invoices').findOne({
                    transferIds: relatedTransferId,
                    $select: ['_id', 'transferIds', 'deliveries', 'relatedDocuments']
                });
            }
            if (!!document) {
                const deliveries = document.deliveries ?? [];
                const index = deliveries.findIndex(delivery => delivery.deliveryId === transfer._id);
                let deliveryStatus = 'waiting';

                if (index === -1) {
                    const delivery = {};

                    delivery.deliveryId = transfer._id;
                    delivery.deliveryCode = transfer.code;
                    delivery.deliveryDate = transfer.scheduledDate;
                    delivery.deliveryDocumentNo = transfer.documentNo;
                    delivery.deliveryStatus = !!payload.status ? payload.status : transfer.status;

                    deliveries.push(delivery);
                } else {
                    deliveries[index].deliveryCode = transfer.code;
                    deliveries[index].deliveryDate = transfer.scheduledDate;
                    deliveries[index].deliveryDocumentNo = transfer.documentNo;
                    deliveries[index].deliveryStatus = !!payload.status ? payload.status : transfer.status;
                }

                if (deliveries.every(delivery => delivery.deliveryStatus === 'approved')) {
                    deliveryStatus = 'delivered';

                    const hasReservations =
                        (await app.collection('inventory.external-reservations').count({
                            documentId: document._id,
                            $or: [{assigned: {$gt: 0}}, {ordered: {$gt: 0}}]
                        })) > 0;
                    if (hasReservations) {
                        deliveryStatus = 'partially-delivered';
                    }
                } else if (deliveries.some(delivery => delivery.deliveryStatus === 'approved')) {
                    deliveryStatus = 'partially-delivered';
                } else if (deliveries.every(delivery => delivery.deliveryStatus === 'canceled')) {
                    deliveryStatus = 'canceled';
                }

                await app.collection('accounting.vendor-invoices').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: document._id},
                            update: {$set: {deliveries, deliveryStatus}}
                        }
                    }
                ]);

                if (
                    transfer.referenceCollection === 'accounting.vendor-invoices' &&
                    Array.isArray(document.relatedDocuments) &&
                    document.relatedDocuments.length > 0
                ) {
                    const purchaseOrderDocument = document.relatedDocuments.find(
                        rd => rd.collection === 'purchase.orders'
                    );

                    if (
                        purchaseOrderDocument &&
                        Array.isArray(purchaseOrderDocument.ids) &&
                        purchaseOrderDocument.ids.length > 0
                    ) {
                        const order = await app.collection('purchase.orders').findOne({
                            _id: purchaseOrderDocument.ids[0],
                            $select: ['_id', 'transferIds', 'deliveries'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });

                        if (!!order) {
                            const deliveries = order.deliveries ?? [];
                            const index = deliveries.findIndex(delivery => delivery.deliveryId === transfer._id);
                            let deliveryStatus = 'waiting';

                            if (index === -1) {
                                const delivery = {};

                                delivery.deliveryId = transfer._id;
                                delivery.deliveryCode = transfer.code;
                                delivery.deliveryDate = transfer.scheduledDate;
                                delivery.deliveryDocumentNo = transfer.documentNo;
                                delivery.deliveryStatus = !!payload.status ? payload.status : transfer.status;

                                deliveries.push(delivery);
                            } else {
                                deliveries[index].deliveryCode = transfer.code;
                                deliveries[index].deliveryDate = transfer.scheduledDate;
                                deliveries[index].deliveryDocumentNo = transfer.documentNo;
                                deliveries[index].deliveryStatus = !!payload.status ? payload.status : transfer.status;
                            }

                            if (deliveries.every(delivery => delivery.deliveryStatus === 'approved')) {
                                deliveryStatus = 'delivered';

                                const hasReservations =
                                    (await app.collection('inventory.external-reservations').count({
                                        documentId: order._id,
                                        $or: [{assigned: {$gt: 0}}, {ordered: {$gt: 0}}]
                                    })) > 0;
                                if (hasReservations) {
                                    deliveryStatus = 'partially-delivered';
                                }
                            } else if (deliveries.some(delivery => delivery.deliveryStatus === 'approved')) {
                                deliveryStatus = 'partially-delivered';
                            } else if (deliveries.every(delivery => delivery.deliveryStatus === 'canceled')) {
                                deliveryStatus = 'canceled';
                            }

                            await app.collection('purchase.orders').bulkWrite([
                                {
                                    updateOne: {
                                        filter: {_id: order._id},
                                        update: {$set: {deliveries, deliveryStatus}}
                                    }
                                }
                            ]);
                        }
                    }
                }
            }
        }
    }
];

async function saveCopiedDocumentRecords(app, id, data) {
    if (Array.isArray(data.copiedDocumentRecords)) {
        const copiedDocumentRecords = data.copiedDocumentRecords;

        await app.db.collection('kernel_copied-document-records').deleteMany(
            {
                destinationDocumentCollection: 'inventory.transfers',
                destinationDocumentId: id
            },
            {
                collation: {locale: app.config('app.locale')}
            }
        );

        if (copiedDocumentRecords.length > 0) {
            await app.db.collection('kernel_copied-document-records').insertMany(copiedDocumentRecords);
        }
    }
}
